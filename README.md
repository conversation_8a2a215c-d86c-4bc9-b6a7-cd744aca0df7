# Deriv Rise/Fall Trading Bot

An automated trading bot for Deriv's rise/fall options using WebSocket API connections. This bot implements various trading strategies with comprehensive risk management and monitoring capabilities.

## Features

- 🤖 **Automated Trading**: Execute rise/fall trades based on configurable strategies
- 📊 **Multiple Strategies**: Simple trend following and moving average crossover strategies
- 🛡️ **Risk Management**: Daily loss limits, consecutive loss protection, and position sizing
- 📈 **Real-time Monitoring**: Live market data processing and trade execution
- 📝 **Comprehensive Logging**: Detailed logs for trading activities, errors, and performance
- ⚙️ **Configurable**: Extensive configuration options via environment variables
- 🧪 **Testing Support**: Built-in testing framework and demo mode

## Prerequisites

- Node.js 16.0 or higher
- A Deriv account
- Deriv App ID (get from [Deriv API Dashboard](https://api.deriv.com/dashboard))
- Deriv API Token (optional, for live trading)

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd deriv-rise-fall-bot
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   # Required
   DERIV_APP_ID=your_app_id_here
   
   # Optional (for live trading)
   DERIV_API_TOKEN=your_api_token_here
   
   # Trading Configuration
   DEFAULT_STAKE=10
   DEFAULT_CURRENCY=USD
   DEFAULT_SYMBOL=R_100
   TRADING_ENABLED=false
   ```

4. **Build the project**:
   ```bash
   npm run build
   ```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Testing
```bash
npm test
```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DERIV_APP_ID` | Your Deriv App ID | - | Yes |
| `DERIV_API_TOKEN` | Your API token for live trading | - | No |
| `TRADING_ENABLED` | Enable/disable actual trading | `false` | No |
| `DEFAULT_STAKE` | Default stake amount | `10` | No |
| `DEFAULT_CURRENCY` | Trading currency | `USD` | No |
| `DEFAULT_SYMBOL` | Trading symbol | `R_100` | No |
| `MAX_DAILY_LOSS` | Maximum daily loss limit | `100` | No |
| `MAX_CONSECUTIVE_LOSSES` | Max consecutive losses before stopping | `3` | No |
| `STRATEGY_TYPE` | Trading strategy to use | `simple_trend` | No |

### Trading Strategies

#### 1. Simple Trend Strategy (`simple_trend`)
- Analyzes price trends over a configurable lookback period
- Generates signals based on trend direction and momentum
- Adjusts confidence based on volatility

#### 2. Moving Average Crossover (`ma_crossover`)
- Uses short and long-term moving averages
- Generates signals when averages cross over
- Suitable for trending markets

### Risk Management

The bot includes several risk management features:

- **Daily Loss Limit**: Stops trading when daily losses exceed the configured limit
- **Consecutive Loss Protection**: Pauses trading after a series of consecutive losses
- **Position Sizing**: Configurable stake amounts and percentage-based sizing
- **Signal Confidence**: Only executes trades above a minimum confidence threshold

## API Integration

The bot connects to Deriv's WebSocket API for:
- Real-time market data (ticks)
- Account information and balance
- Trade execution and monitoring
- Contract status updates

## Logging

Comprehensive logging is implemented with different levels:
- **Error logs**: `logs/error.log`
- **Combined logs**: `logs/combined.log`
- **Trading logs**: `logs/trading.log`
- **Console output**: Development mode only

## Safety Features

⚠️ **Important Safety Notes**:

1. **Demo Mode**: Set `TRADING_ENABLED=false` for testing without real money
2. **Start Small**: Begin with small stake amounts when testing
3. **Monitor Closely**: Always monitor the bot's performance
4. **Risk Limits**: Configure appropriate risk management settings
5. **API Tokens**: Keep your API tokens secure and never share them

## Project Structure

```
src/
├── bot/
│   └── DerivBot.ts          # Main bot orchestrator
├── config/
│   └── ConfigManager.ts     # Configuration management
├── services/
│   └── DerivAPIService.ts   # Deriv API WebSocket client
├── strategies/
│   └── TradingStrategy.ts   # Trading strategy implementations
├── types/
│   └── index.ts             # TypeScript type definitions
├── utils/
│   └── Logger.ts            # Logging and monitoring utilities
└── index.ts                 # Application entry point
```

## Getting Your Deriv Credentials

1. **App ID**:
   - Visit [Deriv API Dashboard](https://api.deriv.com/dashboard)
   - Register or log in to your account
   - Create a new app to get your App ID

2. **API Token** (for live trading):
   - In the API Dashboard, go to "Manage Tokens"
   - Create a new token with trading permissions
   - **Keep this token secure and never share it**

## Development

### Adding New Strategies

1. Extend the `TradingStrategy` abstract class
2. Implement the `generateSignal()` method
3. Add your strategy to the factory in `DerivBot.ts`

### Running Tests

```bash
npm test              # Run all tests
npm run test:watch    # Run tests in watch mode
```

### Linting

```bash
npm run lint          # Check for linting errors
npm run lint:fix      # Fix linting errors automatically
```

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check your internet connection and App ID
2. **Authorization Errors**: Verify your API token is correct and has trading permissions
3. **Trading Disabled**: Ensure `TRADING_ENABLED=true` for live trading
4. **No Signals**: Check strategy parameters and market conditions

### Debug Mode

Set `LOG_LEVEL=debug` in your `.env` file for detailed logging.

## Disclaimer

⚠️ **Trading Risk Warning**: 
- Trading financial instruments involves substantial risk of loss
- Past performance does not guarantee future results
- Only trade with money you can afford to lose
- This bot is for educational purposes and comes with no guarantees
- Always test thoroughly in demo mode before live trading

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the logs for error details
2. Review the configuration settings
3. Test in demo mode first
4. Consult Deriv API documentation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request
