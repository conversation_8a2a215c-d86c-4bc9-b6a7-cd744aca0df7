#!/usr/bin/env ts-node

import { DerivBot } from './src/bot/DerivBot';
import { ConfigManager } from './src/config/ConfigManager';
import { Logger } from './src/utils/Logger';

const logger = Logger.getInstance();

async function runDemo() {
  logger.info('🚀 Starting Deriv Bot Demo with your API key...');
  
  try {
    // Get configuration
    const config = ConfigManager.getInstance();
    const derivConfig = config.getDerivAPIConfig();
    
    logger.info('📊 Configuration loaded:');
    logger.info(`- Environment: ${config.getEnvironment()}`);
    logger.info(`- Trading Enabled: ${config.isTradingEnabled()}`);
    logger.info(`- Symbol: ${config.getTradingConfig().symbol}`);
    logger.info(`- Stake: ${config.getTradingConfig().stake} ${config.getTradingConfig().currency}`);
    logger.info(`- Strategy: ${config.getBotConfig().strategy.type}`);
    logger.info(`- API Token: ${derivConfig.apiToken?.substring(0, 8)}...`);
    
    // Create and configure bot
    const bot = new DerivBot();
    
    // Set up event listeners for monitoring
    bot.on('signal_generated', (signal) => {
      logger.info(`📈 Signal Generated: ${signal.type} (confidence: ${signal.confidence.toFixed(2)})`);
    });
    
    bot.on('trading_stopped', (reason) => {
      logger.warn(`⏹️ Trading Stopped: ${reason}`);
    });
    
    bot.on('error', (error) => {
      logger.error(`❌ Bot Error: ${error.message}`);
    });
    
    bot.on('stopped', () => {
      logger.info('🛑 Bot has been stopped');
      process.exit(0);
    });
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('🔄 Received SIGINT, shutting down gracefully...');
      await bot.stop();
    });
    
    process.on('SIGTERM', async () => {
      logger.info('🔄 Received SIGTERM, shutting down gracefully...');
      await bot.stop();
    });
    
    // Start the bot
    logger.info('🎯 Starting bot...');
    await bot.start();
    
    logger.info('✅ Bot started successfully! Monitoring for signals...');
    logger.info('💡 Press Ctrl+C to stop the bot');
    
    // Keep the process running
    setInterval(() => {
      // Bot status check every 30 seconds
      const stats = bot.getStats();
      if (stats.isActive) {
        logger.debug('🔄 Bot is running and monitoring market...');
      }
    }, 30000);

  } catch (error) {
    logger.error('💥 Failed to start demo:', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

// Run the demo
runDemo().catch((error) => {
  logger.error('💥 Demo crashed:', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
});
