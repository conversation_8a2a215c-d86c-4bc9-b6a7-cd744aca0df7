import { EventEmitter } from 'events';
import { TradeResult, BotStats } from '../types';
export declare class DerivBot extends EventEmitter {
    private api;
    private config;
    private logger;
    private monitor;
    private strategy;
    private isRunning;
    private stats;
    private tradeHistory;
    private currentBalance;
    private dailyProfit;
    private lastResetDate;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    private startTrading;
    private startMonitoring;
    private handleTick;
    private executeTrade;
    private canTrade;
    private shouldExecuteSignal;
    private createStrategy;
    private setupEventListeners;
    private handleBalanceUpdate;
    private handleContractUpdate;
    private updateBalance;
    private checkDailyReset;
    private logStatistics;
    getStats(): BotStats;
    getTradeHistory(): TradeResult[];
}
//# sourceMappingURL=DerivBot.d.ts.map