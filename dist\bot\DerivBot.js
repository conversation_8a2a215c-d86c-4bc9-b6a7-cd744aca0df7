"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DerivBot = void 0;
const events_1 = require("events");
const DerivAPIService_1 = require("../services/DerivAPIService");
const ConfigManager_1 = require("../config/ConfigManager");
const Logger_1 = require("../utils/Logger");
const TradingStrategy_1 = require("../strategies/TradingStrategy");
class DerivBot extends events_1.EventEmitter {
    constructor() {
        super();
        this.isRunning = false;
        this.stats = {
            totalTrades: 0,
            winRate: 0,
            totalProfit: 0,
            dailyProfit: 0,
            consecutiveLosses: 0,
            isActive: false,
            lastTradeTime: 0,
        };
        this.tradeHistory = [];
        this.dailyProfit = 0;
        this.lastResetDate = new Date().toDateString();
        this.config = ConfigManager_1.ConfigManager.getInstance();
        this.logger = Logger_1.Logger.getInstance();
        this.monitor = Logger_1.PerformanceMonitor.getInstance();
        const apiConfig = this.config.getDerivAPIConfig();
        this.api = new DerivAPIService_1.DerivAPIService(apiConfig);
        this.strategy = this.createStrategy();
        this.setupEventListeners();
    }
    async start() {
        if (this.isRunning) {
            throw new Error('Bot is already running');
        }
        try {
            this.logger.info('Starting Deriv Bot...');
            await this.api.connect();
            this.logger.info('Connected to Deriv API');
            if (this.config.getDerivAPIConfig().apiToken) {
                await this.api.authorize();
                this.logger.info('Authorized with Deriv API');
                await this.updateBalance();
            }
            if (this.config.isTradingEnabled()) {
                await this.startTrading();
            }
            else {
                this.logger.info('Trading is disabled. Bot will only monitor market data.');
                await this.startMonitoring();
            }
            this.isRunning = true;
            this.stats.isActive = true;
            this.emit('started');
        }
        catch (error) {
            this.logger.error('Failed to start bot', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    async stop() {
        if (!this.isRunning) {
            return;
        }
        this.logger.info('Stopping Deriv Bot...');
        this.isRunning = false;
        this.stats.isActive = false;
        this.api.disconnect();
        this.logStatistics();
        this.emit('stopped');
        this.logger.info('Bot stopped');
    }
    async startTrading() {
        const tradingConfig = this.config.getTradingConfig();
        this.logger.info('Starting trading mode', { symbol: tradingConfig.symbol });
        await this.api.subscribeTicks(tradingConfig.symbol);
        this.logger.info(`Subscribed to ${tradingConfig.symbol} ticks`);
    }
    async startMonitoring() {
        const tradingConfig = this.config.getTradingConfig();
        this.logger.info('Starting monitoring mode', { symbol: tradingConfig.symbol });
        await this.api.subscribeTicks(tradingConfig.symbol);
        this.logger.info(`Monitoring ${tradingConfig.symbol} ticks`);
    }
    async handleTick(tickData) {
        const stopTimer = this.monitor.startTimer('handleTick');
        try {
            this.strategy.addTick(tickData);
            if (this.config.isTradingEnabled() && this.canTrade()) {
                const signal = this.strategy.generateSignal();
                if (signal && this.shouldExecuteSignal(signal)) {
                    await this.executeTrade(signal);
                }
            }
            this.checkDailyReset();
        }
        catch (error) {
            this.logger.error('Error handling tick', error instanceof Error ? error : new Error(String(error)));
        }
        finally {
            stopTimer();
        }
    }
    async executeTrade(signal) {
        const stopTimer = this.monitor.startTimer('executeTrade');
        try {
            const tradingConfig = this.config.getTradingConfig();
            this.logger.trade('Executing trade', {
                signal,
                stake: tradingConfig.stake,
                symbol: tradingConfig.symbol,
            });
            const proposalRequest = {
                proposal: 1,
                amount: tradingConfig.stake,
                basis: 'stake',
                contract_type: signal.direction === 'CALL' ? 'CALL' : 'PUT',
                currency: tradingConfig.currency,
                duration: tradingConfig.duration,
                duration_unit: tradingConfig.durationUnit,
                symbol: tradingConfig.symbol,
            };
            const proposal = await this.api.getPriceProposal(proposalRequest);
            if (!proposal.proposal) {
                throw new Error('No proposal received');
            }
            const buyRequest = {
                buy: proposal.proposal.id,
                price: proposal.proposal.ask_price,
            };
            const buyResponse = await this.api.buyContract(buyRequest);
            if (buyResponse.buy) {
                const trade = {
                    id: buyResponse.buy.contract_id.toString(),
                    symbol: tradingConfig.symbol,
                    contractType: signal.direction,
                    stake: buyResponse.buy.buy_price,
                    payout: buyResponse.buy.payout,
                    profit: 0,
                    timestamp: Date.now(),
                    status: 'pending',
                };
                this.tradeHistory.push(trade);
                this.stats.totalTrades++;
                this.stats.lastTradeTime = Date.now();
                await this.api.getContract(buyResponse.buy.contract_id);
                this.logger.trade('Trade executed successfully', {
                    contractId: buyResponse.buy.contract_id,
                    stake: buyResponse.buy.buy_price,
                    payout: buyResponse.buy.payout,
                });
                this.emit('trade_executed', trade);
            }
        }
        catch (error) {
            this.logger.error('Failed to execute trade', error instanceof Error ? error : new Error(String(error)));
            this.emit('trade_error', error);
        }
        finally {
            stopTimer();
        }
    }
    canTrade() {
        const riskConfig = this.config.getRiskConfig();
        if (this.dailyProfit <= -riskConfig.maxDailyLoss) {
            this.logger.risk('Daily loss limit reached', {
                dailyProfit: this.dailyProfit,
                maxDailyLoss: riskConfig.maxDailyLoss,
            });
            return false;
        }
        if (this.stats.consecutiveLosses >= riskConfig.maxConsecutiveLosses) {
            this.logger.risk('Consecutive losses limit reached', {
                consecutiveLosses: this.stats.consecutiveLosses,
                maxConsecutiveLosses: riskConfig.maxConsecutiveLosses,
            });
            return false;
        }
        return true;
    }
    shouldExecuteSignal(signal) {
        const strategyConfig = this.config.getBotConfig().strategy;
        const threshold = strategyConfig.parameters['signalThreshold'] || 0.6;
        if (signal.confidence < threshold) {
            return false;
        }
        const minTimeBetweenTrades = 60000;
        if (Date.now() - this.stats.lastTradeTime < minTimeBetweenTrades) {
            return false;
        }
        return true;
    }
    createStrategy() {
        const strategyConfig = this.config.getBotConfig().strategy;
        switch (strategyConfig.type) {
            case 'simple_trend':
                return new TradingStrategy_1.SimpleTrendStrategy(strategyConfig.parameters['signalThreshold'] || 0.6, strategyConfig.parameters['lookbackPeriods'] || 10);
            case 'ma_crossover':
                return new TradingStrategy_1.MovingAverageCrossoverStrategy(5, 15, 0.7);
            default:
                this.logger.warn(`Unknown strategy type: ${strategyConfig.type}, using simple_trend`);
                return new TradingStrategy_1.SimpleTrendStrategy();
        }
    }
    setupEventListeners() {
        this.api.on('tick', (tickData) => {
            this.handleTick(tickData).catch((error) => {
                this.logger.error('Error in tick handler', error);
            });
        });
        this.api.on('balance', (balanceData) => {
            this.handleBalanceUpdate(balanceData);
        });
        this.api.on('contract_update', (contractData) => {
            this.handleContractUpdate(contractData);
        });
        this.api.on('error', (error) => {
            this.logger.error('API error', error);
            this.emit('error', error);
        });
        this.api.on('disconnected', () => {
            this.logger.warn('API disconnected');
            this.emit('disconnected');
        });
    }
    handleBalanceUpdate(balanceData) {
        this.logger.debug('Balance updated', balanceData);
    }
    handleContractUpdate(contractData) {
        this.logger.debug('Contract updated', contractData);
    }
    async updateBalance() {
        try {
            const balanceResponse = await this.api.getBalance();
            this.logger.debug('Balance retrieved', balanceResponse);
        }
        catch (error) {
            this.logger.error('Failed to get balance', error instanceof Error ? error : new Error(String(error)));
        }
    }
    checkDailyReset() {
        const currentDate = new Date().toDateString();
        if (currentDate !== this.lastResetDate) {
            this.dailyProfit = 0;
            this.lastResetDate = currentDate;
            this.logger.info('Daily stats reset');
        }
    }
    logStatistics() {
        this.logger.info('Bot Statistics', {
            stats: this.stats,
            dailyProfit: this.dailyProfit,
            totalTrades: this.tradeHistory.length,
        });
    }
    getStats() {
        return { ...this.stats };
    }
    getTradeHistory() {
        return [...this.tradeHistory];
    }
}
exports.DerivBot = DerivBot;
//# sourceMappingURL=DerivBot.js.map