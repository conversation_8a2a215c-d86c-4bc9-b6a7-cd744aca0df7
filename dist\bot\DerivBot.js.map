{"version": 3, "file": "DerivBot.js", "sourceRoot": "", "sources": ["../../src/bot/DerivBot.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,iEAA8D;AAC9D,2DAAwD;AACxD,4CAA6D;AAC7D,mEAAqH;AAUrH,MAAa,QAAS,SAAQ,qBAAY;IAuBxC;QACE,KAAK,EAAE,CAAC;QAjBF,cAAS,GAAG,KAAK,CAAC;QAClB,UAAK,GAAa;YACxB,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,CAAC;SACjB,CAAC;QAEM,iBAAY,GAAkB,EAAE,CAAC;QACjC,mBAAc,GAAG,CAAC,CAAC;QACnB,gBAAW,GAAG,CAAC,CAAC;QAChB,kBAAa,GAAG,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;QAIhD,IAAI,CAAC,MAAM,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,2BAAkB,CAAC,WAAW,EAAE,CAAC;QAGhD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAClD,IAAI,CAAC,GAAG,GAAG,IAAI,iCAAe,CAAC,SAAS,CAAC,CAAC;QAG1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAKM,KAAK,CAAC,KAAK;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAG1C,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAG3C,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,QAAQ,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAG9C,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,CAAC;YAGD,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;gBAC5E,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/B,CAAC;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE1C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;QAG5B,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QAGtB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IAKO,KAAK,CAAC,YAAY;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QAG5E,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,aAAa,CAAC,MAAM,QAAQ,CAAC,CAAC;IAClE,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QAG/E,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,aAAa,CAAC,MAAM,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAKO,KAAK,CAAC,UAAU,CAAC,QAAkB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAExD,IAAI,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAGhC,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACtD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAE9C,IAAI,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC/C,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAGD,IAAI,CAAC,eAAe,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC;gBAAS,CAAC;YACT,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,MAAqB;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAE1D,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;gBACnC,MAAM;gBACN,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B,CAAC,CAAC;YAGH,MAAM,eAAe,GAAoB;gBACvC,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,aAAa,CAAC,KAAK;gBAC3B,KAAK,EAAE,OAAO;gBACd,aAAa,EAAE,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;gBAC3D,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,aAAa,EAAE,aAAa,CAAC,YAAY;gBACzC,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B,CAAC;YAGF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAElE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAGD,MAAM,UAAU,GAAe;gBAC7B,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACzB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,SAAS;aACnC,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAE3D,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;gBACpB,MAAM,KAAK,GAAgB;oBACzB,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE;oBAC1C,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,YAAY,EAAE,MAAM,CAAC,SAAS;oBAC9B,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS;oBAChC,MAAM,EAAE,WAAW,CAAC,GAAG,CAAC,MAAM;oBAC9B,MAAM,EAAE,CAAC;oBACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,MAAM,EAAE,SAAS;iBAClB,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAGtC,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAExD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;oBAC/C,UAAU,EAAE,WAAW,CAAC,GAAG,CAAC,WAAW;oBACvC,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS;oBAChC,MAAM,EAAE,WAAW,CAAC,GAAG,CAAC,MAAM;iBAC/B,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACrC,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;gBAAS,CAAC;YACT,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAKO,QAAQ;QACd,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAG/C,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAC3C,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,YAAY,EAAE,UAAU,CAAC,YAAY;aACtC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC,oBAAoB,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;gBACnD,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;gBAC/C,oBAAoB,EAAE,UAAU,CAAC,oBAAoB;aACtD,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,mBAAmB,CAAC,MAAqB;QAE/C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;QAC3D,MAAM,SAAS,GAAI,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAY,IAAI,GAAG,CAAC;QAElF,IAAI,MAAM,CAAC,UAAU,GAAG,SAAS,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,MAAM,oBAAoB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,oBAAoB,EAAE,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,cAAc;QACpB,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;QAE3D,QAAQ,cAAc,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,cAAc;gBACjB,OAAO,IAAI,qCAAmB,CAC3B,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAY,IAAI,GAAG,EAC9D,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAY,IAAI,EAAE,CAC/D,CAAC;YACJ,KAAK,cAAc;gBACjB,OAAO,IAAI,gDAA8B,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YACxD;gBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,cAAc,CAAC,IAAI,sBAAsB,CAAC,CAAC;gBACtF,OAAO,IAAI,qCAAmB,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAKO,mBAAmB;QACzB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,QAAkB,EAAE,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,WAAoB,EAAE,EAAE;YAC9C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,YAAqB,EAAE,EAAE;YACvD,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,mBAAmB,CAAC,WAAoB;QAE9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,WAAsC,CAAC,CAAC;IAC/E,CAAC;IAKO,oBAAoB,CAAC,YAAqB;QAEhD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,YAAuC,CAAC,CAAC;IACjF,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YAEpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,eAA0C,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAKO,eAAe;QACrB,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAI,WAAW,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;YACvC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAKO,aAAa;QACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;SACtC,CAAC,CAAC;IACL,CAAC;IAKM,QAAQ;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAKM,eAAe;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;CACF;AAjZD,4BAiZC"}