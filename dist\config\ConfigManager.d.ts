import { BotConfig, TradeConfig, RiskManagement } from '../types';
export declare class ConfigManager {
    private static instance;
    private config;
    private constructor();
    static getInstance(): ConfigManager;
    private loadConfiguration;
    private validateConfiguration;
    getDerivAPIConfig(): {
        appId: string;
        apiToken?: string;
        apiUrl?: string;
    };
    getBotConfig(): BotConfig;
    getTradingConfig(): TradeConfig;
    getRiskConfig(): RiskManagement;
    updateTradingConfig(updates: Partial<TradeConfig>): void;
    updateRiskConfig(updates: Partial<RiskManagement>): void;
    isTradingEnabled(): boolean;
    getEnvironment(): string;
    getLogLevel(): string;
    private getEnvString;
    private getEnvNumber;
    private getEnvBoolean;
    exportConfig(): string;
    importConfig(configJson: string): void;
}
//# sourceMappingURL=ConfigManager.d.ts.map