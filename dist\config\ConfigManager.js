"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const dotenv = __importStar(require("dotenv"));
dotenv.config();
class ConfigManager {
    constructor() {
        this.config = this.loadConfiguration();
        this.validateConfiguration();
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    loadConfiguration() {
        return {
            trading: {
                symbol: this.getEnvString('DEFAULT_SYMBOL', 'R_100'),
                stake: this.getEnvNumber('DEFAULT_STAKE', 10),
                currency: this.getEnvString('DEFAULT_CURRENCY', 'USD'),
                duration: this.getEnvNumber('DEFAULT_DURATION', 5),
                durationUnit: this.getEnvString('DEFAULT_DURATION_UNIT', 'min'),
                contractType: 'CALL',
                allowEquals: false,
            },
            risk: {
                maxDailyLoss: this.getEnvNumber('MAX_DAILY_LOSS', 100),
                maxConsecutiveLosses: this.getEnvNumber('MAX_CONSECUTIVE_LOSSES', 3),
                stopLossPercentage: this.getEnvNumber('STOP_LOSS_PERCENTAGE', 50),
                maxStakePercentage: this.getEnvNumber('MAX_STAKE_PERCENTAGE', 10),
            },
            strategy: {
                type: this.getEnvString('STRATEGY_TYPE', 'simple_trend'),
                parameters: {
                    signalThreshold: this.getEnvNumber('SIGNAL_THRESHOLD', 0.6),
                    lookbackPeriods: this.getEnvNumber('LOOKBACK_PERIODS', 10),
                },
            },
        };
    }
    validateConfiguration() {
        const errors = [];
        if (this.config.trading.stake <= 0) {
            errors.push('Stake must be greater than 0');
        }
        if (this.config.trading.duration <= 0) {
            errors.push('Duration must be greater than 0');
        }
        if (!['min', 'h', 'd', 't', 's'].includes(this.config.trading.durationUnit)) {
            errors.push('Invalid duration unit');
        }
        if (this.config.risk.maxDailyLoss <= 0) {
            errors.push('Max daily loss must be greater than 0');
        }
        if (this.config.risk.maxConsecutiveLosses <= 0) {
            errors.push('Max consecutive losses must be greater than 0');
        }
        if (this.config.risk.stopLossPercentage <= 0 || this.config.risk.stopLossPercentage > 100) {
            errors.push('Stop loss percentage must be between 0 and 100');
        }
        if (this.config.risk.maxStakePercentage <= 0 || this.config.risk.maxStakePercentage > 100) {
            errors.push('Max stake percentage must be between 0 and 100');
        }
        if (errors.length > 0) {
            throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
        }
    }
    getDerivAPIConfig() {
        const appId = this.getEnvString('DERIV_APP_ID');
        if (!appId) {
            throw new Error('DERIV_APP_ID is required');
        }
        return {
            appId,
            apiToken: this.getEnvString('DERIV_API_TOKEN'),
            apiUrl: this.getEnvString('DERIV_API_URL'),
        };
    }
    getBotConfig() {
        return { ...this.config };
    }
    getTradingConfig() {
        return { ...this.config.trading };
    }
    getRiskConfig() {
        return { ...this.config.risk };
    }
    updateTradingConfig(updates) {
        this.config.trading = { ...this.config.trading, ...updates };
        this.validateConfiguration();
    }
    updateRiskConfig(updates) {
        this.config.risk = { ...this.config.risk, ...updates };
        this.validateConfiguration();
    }
    isTradingEnabled() {
        return this.getEnvBoolean('TRADING_ENABLED', false);
    }
    getEnvironment() {
        return this.getEnvString('ENVIRONMENT', 'demo');
    }
    getLogLevel() {
        return this.getEnvString('LOG_LEVEL', 'info');
    }
    getEnvString(key, defaultValue) {
        const value = process.env[key];
        if (value === undefined) {
            if (defaultValue !== undefined) {
                return defaultValue;
            }
            throw new Error(`Environment variable ${key} is required`);
        }
        return value;
    }
    getEnvNumber(key, defaultValue) {
        const value = process.env[key];
        if (value === undefined) {
            if (defaultValue !== undefined) {
                return defaultValue;
            }
            throw new Error(`Environment variable ${key} is required`);
        }
        const parsed = parseFloat(value);
        if (isNaN(parsed)) {
            throw new Error(`Environment variable ${key} must be a valid number`);
        }
        return parsed;
    }
    getEnvBoolean(key, defaultValue) {
        const value = process.env[key];
        if (value === undefined) {
            if (defaultValue !== undefined) {
                return defaultValue;
            }
            throw new Error(`Environment variable ${key} is required`);
        }
        return value.toLowerCase() === 'true';
    }
    exportConfig() {
        return JSON.stringify(this.config, null, 2);
    }
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            this.config = importedConfig;
            this.validateConfiguration();
        }
        catch (error) {
            throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map