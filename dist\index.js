#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.ConfigManager = exports.DerivAPIService = exports.DerivBot = void 0;
const DerivBot_1 = require("./bot/DerivBot");
const Logger_1 = require("./utils/Logger");
const ConfigManager_1 = require("./config/ConfigManager");
async function main() {
    const logger = Logger_1.Logger.getInstance();
    const config = ConfigManager_1.ConfigManager.getInstance();
    logger.info('='.repeat(50));
    logger.info('🤖 Deriv Rise/Fall Trading Bot Starting...');
    logger.info('='.repeat(50));
    try {
        const derivConfig = config.getDerivAPIConfig();
        if (!derivConfig.appId) {
            throw new Error('DERIV_APP_ID is required. Please check your .env file.');
        }
        logger.info('Configuration loaded successfully', {
            environment: config.getEnvironment(),
            tradingEnabled: config.isTradingEnabled(),
            symbol: config.getTradingConfig().symbol,
            strategy: config.getBotConfig().strategy.type,
        });
        const bot = new DerivBot_1.DerivBot();
        bot.on('started', () => {
            logger.info('✅ Bot started successfully');
        });
        bot.on('stopped', () => {
            logger.info('🛑 Bot stopped');
        });
        bot.on('trade_executed', (trade) => {
            logger.trade('Trade executed', trade);
        });
        bot.on('trade_error', (error) => {
            logger.error('Trade execution failed', error);
        });
        bot.on('error', (error) => {
            logger.error('Bot error', error);
        });
        bot.on('disconnected', () => {
            logger.warn('Bot disconnected from API');
        });
        const shutdown = async (signal) => {
            logger.info(`Received ${signal}. Shutting down gracefully...`);
            try {
                await bot.stop();
                process.exit(0);
            }
            catch (error) {
                logger.error('Error during shutdown', error instanceof Error ? error : new Error(String(error)));
                process.exit(1);
            }
        };
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught exception', error);
            shutdown('uncaughtException').catch(() => {
                process.exit(1);
            });
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled rejection', { reason, promise });
            shutdown('unhandledRejection').catch(() => {
                process.exit(1);
            });
        });
        await bot.start();
        logger.info('Bot is running. Press Ctrl+C to stop.');
        setInterval(() => {
            const stats = bot.getStats();
            if (stats.totalTrades > 0) {
                logger.info('Periodic stats update', {
                    totalTrades: stats.totalTrades,
                    winRate: `${(stats.winRate * 100).toFixed(2)}%`,
                    totalProfit: stats.totalProfit,
                    consecutiveLosses: stats.consecutiveLosses,
                    isActive: stats.isActive,
                });
            }
        }, 300000);
    }
    catch (error) {
        logger.error('Failed to start bot', error instanceof Error ? error : new Error(String(error)));
        process.exit(1);
    }
}
if (require.main === module) {
    main().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
var DerivBot_2 = require("./bot/DerivBot");
Object.defineProperty(exports, "DerivBot", { enumerable: true, get: function () { return DerivBot_2.DerivBot; } });
var DerivAPIService_1 = require("./services/DerivAPIService");
Object.defineProperty(exports, "DerivAPIService", { enumerable: true, get: function () { return DerivAPIService_1.DerivAPIService; } });
var ConfigManager_2 = require("./config/ConfigManager");
Object.defineProperty(exports, "ConfigManager", { enumerable: true, get: function () { return ConfigManager_2.ConfigManager; } });
var Logger_2 = require("./utils/Logger");
Object.defineProperty(exports, "Logger", { enumerable: true, get: function () { return Logger_2.Logger; } });
__exportStar(require("./types"), exports);
__exportStar(require("./strategies/TradingStrategy"), exports);
//# sourceMappingURL=index.js.map