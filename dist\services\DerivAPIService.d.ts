import { EventEmitter } from 'events';
import { DerivAPIConfig, ActiveSymbol, ProposalRequest, ProposalResponse, BuyRequest, BuyResponse } from '../types';
export declare class DerivAPIService extends EventEmitter {
    private ws;
    private config;
    private requestId;
    private pendingRequests;
    private isConnected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    constructor(config: DerivAPIConfig);
    connect(): Promise<void>;
    disconnect(): void;
    authorize(): Promise<unknown>;
    getActiveSymbols(): Promise<ActiveSymbol[]>;
    getContractsForSymbol(symbol: string): Promise<unknown>;
    subscribeTicks(symbol: string): Promise<void>;
    unsubscribeTicks(symbol: string): Promise<void>;
    getPriceProposal(request: ProposalRequest): Promise<ProposalResponse>;
    buyContract(request: BuyRequest): Promise<BuyResponse>;
    getBalance(): Promise<unknown>;
    getPortfolio(): Promise<unknown>;
    getContract(contractId: number): Promise<unknown>;
    private sendRequest;
    private handleMessage;
    private handleReconnect;
    private clearPendingRequests;
    isConnectedToAPI(): boolean;
}
//# sourceMappingURL=DerivAPIService.d.ts.map