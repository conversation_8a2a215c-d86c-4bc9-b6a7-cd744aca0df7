"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DerivAPIService = void 0;
const ws_1 = __importDefault(require("ws"));
const events_1 = require("events");
class DerivAPIService extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.ws = null;
        this.requestId = 1;
        this.pendingRequests = new Map();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.config = {
            apiUrl: 'wss://ws.derivws.com/websockets/v3',
            language: 'en',
            ...config,
        };
    }
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                const url = `${this.config.apiUrl}?app_id=${this.config.appId}&l=${this.config.language}`;
                this.ws = new ws_1.default(url);
                this.ws.on('open', () => {
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.emit('connected');
                    resolve();
                });
                this.ws.on('message', (data) => {
                    this.handleMessage(data.toString());
                });
                this.ws.on('close', () => {
                    this.isConnected = false;
                    this.emit('disconnected');
                    this.handleReconnect();
                });
                this.ws.on('error', (error) => {
                    this.emit('error', error);
                    if (!this.isConnected) {
                        reject(error);
                    }
                });
                setTimeout(() => {
                    if (!this.isConnected) {
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
            }
            catch (error) {
                reject(error);
            }
        });
    }
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.clearPendingRequests();
    }
    async authorize() {
        if (!this.config.apiToken) {
            throw new Error('API token is required for authorization');
        }
        return this.sendRequest({
            authorize: this.config.apiToken,
        });
    }
    async getActiveSymbols() {
        const response = await this.sendRequest({
            active_symbols: 'brief',
            product_type: 'basic',
        });
        return response.active_symbols;
    }
    async getContractsForSymbol(symbol) {
        return this.sendRequest({
            contracts_for: symbol,
            product_type: 'basic',
        });
    }
    async subscribeTicks(symbol) {
        await this.sendRequest({
            ticks: symbol,
            subscribe: 1,
        });
    }
    async unsubscribeTicks(symbol) {
        await this.sendRequest({
            forget: symbol,
        });
    }
    async getPriceProposal(request) {
        return this.sendRequest(request);
    }
    async buyContract(request) {
        return this.sendRequest(request);
    }
    async getBalance() {
        return this.sendRequest({
            balance: 1,
            subscribe: 1,
        });
    }
    async getPortfolio() {
        return this.sendRequest({
            portfolio: 1,
        });
    }
    async getContract(contractId) {
        return this.sendRequest({
            proposal_open_contract: 1,
            contract_id: contractId,
            subscribe: 1,
        });
    }
    async sendRequest(request) {
        if (!this.isConnected || !this.ws) {
            throw new Error('WebSocket is not connected');
        }
        const reqId = this.requestId++;
        const message = {
            ...request,
            req_id: reqId,
        };
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(reqId);
                reject(new Error('Request timeout'));
            }, 30000);
            this.pendingRequests.set(reqId, {
                resolve,
                reject,
                timeout,
            });
            this.ws.send(JSON.stringify(message));
        });
    }
    handleMessage(data) {
        try {
            const message = JSON.parse(data);
            if (message.msg_type === 'tick') {
                this.emit('tick', message);
                return;
            }
            if (message.msg_type === 'balance') {
                this.emit('balance', message);
                return;
            }
            if (message.msg_type === 'proposal_open_contract') {
                this.emit('contract_update', message);
                return;
            }
            if (message.req_id && this.pendingRequests.has(message.req_id)) {
                const pending = this.pendingRequests.get(message.req_id);
                this.pendingRequests.delete(message.req_id);
                clearTimeout(pending.timeout);
                if ('error' in message) {
                    const error = message;
                    pending.reject(new Error(`API Error: ${error.error.message} (${error.error.code})`));
                }
                else {
                    pending.resolve(message);
                }
            }
        }
        catch (error) {
            this.emit('error', new Error(`Failed to parse message: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            setTimeout(() => {
                this.connect().catch((error) => {
                    this.emit('error', error);
                });
            }, delay);
        }
        else {
            this.emit('error', new Error('Max reconnection attempts reached'));
        }
    }
    clearPendingRequests() {
        for (const [, pending] of this.pendingRequests) {
            clearTimeout(pending.timeout);
            pending.reject(new Error('Connection closed'));
        }
        this.pendingRequests.clear();
    }
    isConnectedToAPI() {
        return this.isConnected;
    }
}
exports.DerivAPIService = DerivAPIService;
//# sourceMappingURL=DerivAPIService.js.map