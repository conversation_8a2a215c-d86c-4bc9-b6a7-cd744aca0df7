{"version": 3, "file": "DerivAPIService.js", "sourceRoot": "", "sources": ["../../src/services/DerivAPIService.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAA2B;AAC3B,mCAAsC;AAatC,MAAa,eAAgB,SAAQ,qBAAY;IAc/C,YAAY,MAAsB;QAChC,KAAK,EAAE,CAAC;QAdF,OAAE,GAAqB,IAAI,CAAC;QAE5B,cAAS,GAAG,CAAC,CAAC;QACd,oBAAe,GAAG,IAAI,GAAG,EAI7B,CAAC;QACG,gBAAW,GAAG,KAAK,CAAC;QACpB,sBAAiB,GAAG,CAAC,CAAC;QACtB,yBAAoB,GAAG,CAAC,CAAC;QACzB,mBAAc,GAAG,IAAI,CAAC;QAI5B,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM,EAAE,oCAAoC;YAC5C,QAAQ,EAAE,IAAI;YACd,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAKM,KAAK,CAAC,OAAO;QAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC1F,IAAI,CAAC,EAAE,GAAG,IAAI,YAAS,CAAC,GAAG,CAAC,CAAC;gBAE7B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACvB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAoB,EAAE,EAAE;oBAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACvB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtB,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAGH,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBAC1C,CAAC;gBACH,CAAC,EAAE,KAAK,CAAC,CAAC;YACZ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKM,UAAU;QACf,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAKM,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;SAChC,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,gBAAgB;QAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC;YACtC,cAAc,EAAE,OAAO;YACvB,YAAY,EAAE,OAAO;SACtB,CAAuC,CAAC;QAEzC,OAAO,QAAQ,CAAC,cAAc,CAAC;IACjC,CAAC;IAKM,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,aAAa,EAAE,MAAM;YACrB,YAAY,EAAE,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,cAAc,CAAC,MAAc;QACxC,MAAM,IAAI,CAAC,WAAW,CAAC;YACrB,KAAK,EAAE,MAAM;YACb,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,gBAAgB,CAAC,MAAc;QAC1C,MAAM,IAAI,CAAC,WAAW,CAAC;YACrB,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,gBAAgB,CAAC,OAAwB;QACpD,OAAO,IAAI,CAAC,WAAW,CAAC,OAA6C,CAA8B,CAAC;IACtG,CAAC;IAKM,KAAK,CAAC,WAAW,CAAC,OAAmB;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,OAA6C,CAAyB,CAAC;IACjG,CAAC;IAKM,KAAK,CAAC,UAAU;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,YAAY;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAKM,KAAK,CAAC,WAAW,CAAC,UAAkB;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,sBAAsB,EAAE,CAAC;YACzB,WAAW,EAAE,UAAU;YACvB,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,WAAW,CAAC,OAAgC;QACxD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG;YACd,GAAG,OAAO;YACV,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACvC,CAAC,EAAE,KAAK,CAAC,CAAC;YAEV,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE;gBAC9B,OAAO;gBACP,MAAM;gBACN,OAAO;aACR,CAAC,CAAC;YAEH,IAAI,CAAC,EAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,aAAa,CAAC,IAAY;QAChC,IAAI,CAAC;YACH,MAAM,OAAO,GAAc,IAAI,CAAC,KAAK,CAAC,IAAI,CAAc,CAAC;YAGzD,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAA8B,CAAC,CAAC;gBAClD,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC9B,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,KAAK,wBAAwB,EAAE,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACtC,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC;gBAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC5C,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAE9B,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;oBACvB,MAAM,KAAK,GAAG,OAAmC,CAAC;oBAClD,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,cAAc,KAAK,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBACvF,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACxH,CAAC;IACH,CAAC;IAKO,eAAe;QACrB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACvD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;YAE5E,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKO,oBAAoB;QAC1B,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/C,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9B,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAKM,gBAAgB;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AA5RD,0CA4RC"}