import { TradingSignal, TickData } from '../types';
import { Logger } from '../utils/Logger';
export declare abstract class TradingStrategy {
    protected logger: Logger;
    protected tickHistory: TickData[];
    protected maxHistorySize: number;
    constructor();
    abstract generateSignal(): TradingSignal | null;
    addTick(tick: TickData): void;
    protected getLatestTick(): TickData | null;
    protected getTickHistory(count?: number): TickData[];
    protected calculateSMA(period: number): number | null;
    protected calculateEMA(period: number): number | null;
    protected calculatePriceChange(periods?: number): number | null;
    protected calculateVolatility(period: number): number | null;
    protected detectTrend(period?: number): 'up' | 'down' | 'sideways' | null;
    reset(): void;
}
export declare class SimpleTrendStrategy extends TradingStrategy {
    private signalThreshold;
    private lookbackPeriods;
    constructor(signalThreshold?: number, lookbackPeriods?: number);
    generateSignal(): TradingSignal | null;
}
export declare class MovingAverageCrossoverStrategy extends TradingStrategy {
    private shortPeriod;
    private longPeriod;
    private signalThreshold;
    constructor(shortPeriod?: number, longPeriod?: number, signalThreshold?: number);
    generateSignal(): TradingSignal | null;
    private calculateSMAAtIndex;
}
//# sourceMappingURL=TradingStrategy.d.ts.map