"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MovingAverageCrossoverStrategy = exports.SimpleTrendStrategy = exports.TradingStrategy = void 0;
const Logger_1 = require("../utils/Logger");
class TradingStrategy {
    constructor() {
        this.tickHistory = [];
        this.maxHistorySize = 100;
        this.logger = Logger_1.Logger.getInstance();
    }
    addTick(tick) {
        this.tickHistory.push(tick);
        if (this.tickHistory.length > this.maxHistorySize) {
            this.tickHistory = this.tickHistory.slice(-this.maxHistorySize);
        }
        this.logger.debug('Tick added to strategy', {
            symbol: tick.tick.symbol,
            quote: tick.tick.quote,
            historySize: this.tickHistory.length,
        });
    }
    getLatestTick() {
        return this.tickHistory.length > 0 ? this.tickHistory[this.tickHistory.length - 1] : null;
    }
    getTickHistory(count) {
        if (count) {
            return this.tickHistory.slice(-count);
        }
        return [...this.tickHistory];
    }
    calculateSMA(period) {
        if (this.tickHistory.length < period) {
            return null;
        }
        const recentTicks = this.tickHistory.slice(-period);
        const sum = recentTicks.reduce((acc, tick) => acc + tick.tick.quote, 0);
        return sum / period;
    }
    calculateEMA(period) {
        if (this.tickHistory.length < period) {
            return null;
        }
        const multiplier = 2 / (period + 1);
        let ema = this.tickHistory[0].tick.quote;
        for (let i = 1; i < this.tickHistory.length; i++) {
            ema = (this.tickHistory[i].tick.quote * multiplier) + (ema * (1 - multiplier));
        }
        return ema;
    }
    calculatePriceChange(periods = 1) {
        if (this.tickHistory.length < periods + 1) {
            return null;
        }
        const currentPrice = this.tickHistory[this.tickHistory.length - 1].tick.quote;
        const previousPrice = this.tickHistory[this.tickHistory.length - 1 - periods].tick.quote;
        return ((currentPrice - previousPrice) / previousPrice) * 100;
    }
    calculateVolatility(period) {
        if (this.tickHistory.length < period) {
            return null;
        }
        const recentTicks = this.tickHistory.slice(-period);
        const prices = recentTicks.map(tick => tick.tick.quote);
        const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
        const squaredDifferences = prices.map(price => Math.pow(price - mean, 2));
        const variance = squaredDifferences.reduce((sum, diff) => sum + diff, 0) / prices.length;
        return Math.sqrt(variance);
    }
    detectTrend(period = 10) {
        if (this.tickHistory.length < period) {
            return null;
        }
        const recentTicks = this.tickHistory.slice(-period);
        const firstPrice = recentTicks[0].tick.quote;
        const lastPrice = recentTicks[recentTicks.length - 1].tick.quote;
        const priceChange = ((lastPrice - firstPrice) / firstPrice) * 100;
        if (priceChange > 0.1) {
            return 'up';
        }
        else if (priceChange < -0.1) {
            return 'down';
        }
        else {
            return 'sideways';
        }
    }
    reset() {
        this.tickHistory = [];
        this.logger.info('Strategy reset');
    }
}
exports.TradingStrategy = TradingStrategy;
class SimpleTrendStrategy extends TradingStrategy {
    constructor(signalThreshold = 0.6, lookbackPeriods = 10) {
        super();
        this.signalThreshold = signalThreshold;
        this.lookbackPeriods = lookbackPeriods;
        this.maxHistorySize = Math.max(50, lookbackPeriods * 2);
    }
    generateSignal() {
        if (this.tickHistory.length < this.lookbackPeriods) {
            return null;
        }
        const trend = this.detectTrend(this.lookbackPeriods);
        const priceChange = this.calculatePriceChange(this.lookbackPeriods);
        const volatility = this.calculateVolatility(this.lookbackPeriods);
        if (!trend || priceChange === null || volatility === null) {
            return null;
        }
        let confidence = 0;
        let direction = 'CALL';
        let reason = '';
        if (trend === 'up' && priceChange > 0.05) {
            direction = 'CALL';
            confidence = Math.min(0.9, Math.abs(priceChange) / 2 + 0.5);
            reason = `Upward trend detected with ${priceChange.toFixed(2)}% price increase`;
        }
        else if (trend === 'down' && priceChange < -0.05) {
            direction = 'PUT';
            confidence = Math.min(0.9, Math.abs(priceChange) / 2 + 0.5);
            reason = `Downward trend detected with ${priceChange.toFixed(2)}% price decrease`;
        }
        else {
            return null;
        }
        if (volatility > 0.5) {
            confidence *= 0.8;
            reason += ` (high volatility: ${volatility.toFixed(4)})`;
        }
        if (confidence >= this.signalThreshold) {
            const signal = {
                direction,
                confidence,
                timestamp: Date.now(),
                reason,
            };
            this.logger.strategy('Signal generated', {
                signal,
                trend,
                priceChange,
                volatility,
            });
            return signal;
        }
        return null;
    }
}
exports.SimpleTrendStrategy = SimpleTrendStrategy;
class MovingAverageCrossoverStrategy extends TradingStrategy {
    constructor(shortPeriod = 5, longPeriod = 15, signalThreshold = 0.7) {
        super();
        this.shortPeriod = shortPeriod;
        this.longPeriod = longPeriod;
        this.signalThreshold = signalThreshold;
        this.maxHistorySize = Math.max(50, longPeriod * 2);
    }
    generateSignal() {
        if (this.tickHistory.length < this.longPeriod) {
            return null;
        }
        const shortMA = this.calculateSMA(this.shortPeriod);
        const longMA = this.calculateSMA(this.longPeriod);
        if (shortMA === null || longMA === null) {
            return null;
        }
        const prevShortMA = this.calculateSMAAtIndex(this.shortPeriod, 1);
        const prevLongMA = this.calculateSMAAtIndex(this.longPeriod, 1);
        if (prevShortMA === null || prevLongMA === null) {
            return null;
        }
        let signal = null;
        if (prevShortMA <= prevLongMA && shortMA > longMA) {
            const crossoverStrength = (shortMA - longMA) / longMA;
            const confidence = Math.min(0.9, this.signalThreshold + crossoverStrength * 10);
            signal = {
                direction: 'CALL',
                confidence,
                timestamp: Date.now(),
                reason: `Bullish MA crossover: Short MA (${shortMA.toFixed(4)}) > Long MA (${longMA.toFixed(4)})`,
            };
        }
        else if (prevShortMA >= prevLongMA && shortMA < longMA) {
            const crossoverStrength = (longMA - shortMA) / longMA;
            const confidence = Math.min(0.9, this.signalThreshold + crossoverStrength * 10);
            signal = {
                direction: 'PUT',
                confidence,
                timestamp: Date.now(),
                reason: `Bearish MA crossover: Short MA (${shortMA.toFixed(4)}) < Long MA (${longMA.toFixed(4)})`,
            };
        }
        if (signal) {
            this.logger.strategy('MA Crossover signal generated', {
                signal,
                shortMA,
                longMA,
                prevShortMA,
                prevLongMA,
            });
        }
        return signal;
    }
    calculateSMAAtIndex(period, indexFromEnd) {
        if (this.tickHistory.length < period + indexFromEnd) {
            return null;
        }
        const endIndex = this.tickHistory.length - indexFromEnd;
        const startIndex = endIndex - period;
        const relevantTicks = this.tickHistory.slice(startIndex, endIndex);
        const sum = relevantTicks.reduce((acc, tick) => acc + tick.tick.quote, 0);
        return sum / period;
    }
}
exports.MovingAverageCrossoverStrategy = MovingAverageCrossoverStrategy;
//# sourceMappingURL=TradingStrategy.js.map