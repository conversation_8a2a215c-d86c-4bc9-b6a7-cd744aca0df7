{"version": 3, "file": "TradingStrategy.js", "sourceRoot": "", "sources": ["../../src/strategies/TradingStrategy.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAEzC,MAAsB,eAAe;IAKnC;QAHU,gBAAW,GAAe,EAAE,CAAC;QAC7B,mBAAc,GAAW,GAAG,CAAC;QAGrC,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAUM,OAAO,CAAC,IAAc;QAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAG5B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAClD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YAC1C,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;YACxB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;YACtB,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;SACrC,CAAC,CAAC;IACL,CAAC;IAKS,aAAa;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7F,CAAC;IAKS,cAAc,CAAC,KAAc;QACrC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAC/B,CAAC;IAKS,YAAY,CAAC,MAAc;QACnC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxE,OAAO,GAAG,GAAG,MAAM,CAAC;IACtB,CAAC;IAKS,YAAY,CAAC,MAAc;QACnC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpC,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;QAClF,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAKS,oBAAoB,CAAC,UAAkB,CAAC;QAChD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAC/E,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAE1F,OAAO,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;IAChE,CAAC;IAKS,mBAAmB,CAAC,MAAc;QAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAE3E,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAEzF,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAKS,WAAW,CAAC,SAAiB,EAAE;QACvC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAC9C,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAElE,MAAM,WAAW,GAAG,CAAC,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;QAElE,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,WAAW,GAAG,CAAC,GAAG,EAAE,CAAC;YAC9B,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAKM,KAAK;QACV,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACrC,CAAC;CACF;AA9ID,0CA8IC;AAKD,MAAa,mBAAoB,SAAQ,eAAe;IAItD,YAAY,kBAA0B,GAAG,EAAE,kBAA0B,EAAE;QACrE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEM,cAAc;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAElE,IAAI,CAAC,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,SAAS,GAAmB,MAAM,CAAC;QACvC,IAAI,MAAM,GAAG,EAAE,CAAC;QAGhB,IAAI,KAAK,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,EAAE,CAAC;YACzC,SAAS,GAAG,MAAM,CAAC;YACnB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;YAC5D,MAAM,GAAG,8BAA8B,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAClF,CAAC;aAAM,IAAI,KAAK,KAAK,MAAM,IAAI,WAAW,GAAG,CAAC,IAAI,EAAE,CAAC;YACnD,SAAS,GAAG,KAAK,CAAC;YAClB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;YAC5D,MAAM,GAAG,gCAAgC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;QACpF,CAAC;aAAM,CAAC;YAEN,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,UAAU,IAAI,GAAG,CAAC;YAClB,MAAM,IAAI,sBAAsB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,CAAC;QAGD,IAAI,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,MAAM,MAAM,GAAkB;gBAC5B,SAAS;gBACT,UAAU;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM;aACP,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBACvC,MAAM;gBACN,KAAK;gBACL,WAAW;gBACX,UAAU;aACX,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AArED,kDAqEC;AAKD,MAAa,8BAA+B,SAAQ,eAAe;IAKjE,YAAY,cAAsB,CAAC,EAAE,aAAqB,EAAE,EAAE,kBAA0B,GAAG;QACzF,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,cAAc;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAEhE,IAAI,WAAW,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,MAAM,GAAyB,IAAI,CAAC;QAGxC,IAAI,WAAW,IAAI,UAAU,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC;YAClD,MAAM,iBAAiB,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;YAEhF,MAAM,GAAG;gBACP,SAAS,EAAE,MAAM;gBACjB,UAAU;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,mCAAmC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;aAClG,CAAC;QACJ,CAAC;aAEI,IAAI,WAAW,IAAI,UAAU,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC;YACvD,MAAM,iBAAiB,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,MAAM,CAAC;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;YAEhF,MAAM,GAAG;gBACP,SAAS,EAAE,KAAK;gBAChB,UAAU;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,mCAAmC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;aAClG,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,+BAA+B,EAAE;gBACpD,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,WAAW;gBACX,UAAU;aACX,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAE,YAAoB;QAC9D,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,GAAG,YAAY,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,YAAY,CAAC;QACxD,MAAM,UAAU,GAAG,QAAQ,GAAG,MAAM,CAAC;QACrC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEnE,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1E,OAAO,GAAG,GAAG,MAAM,CAAC;IACtB,CAAC;CACF;AArFD,wEAqFC"}