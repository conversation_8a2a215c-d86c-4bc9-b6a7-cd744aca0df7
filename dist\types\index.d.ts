export interface DerivAPIConfig {
    appId: string;
    apiToken?: string;
    apiUrl?: string;
    language?: string;
}
export interface TickData {
    tick: {
        ask: number;
        bid: number;
        epoch: number;
        id: string;
        pip_size: number;
        quote: number;
        symbol: string;
    };
}
export interface ActiveSymbol {
    allow_forward_starting: number;
    display_name: string;
    exchange_is_open: number;
    is_trading_suspended: number;
    market: string;
    market_display_name: string;
    pip: number;
    submarket: string;
    submarket_display_name: string;
    symbol: string;
    symbol_type: string;
}
export interface ContractType {
    contract_type: string;
    contract_display: string;
    sentiment: string;
}
export interface ProposalRequest {
    proposal: number;
    amount: number;
    basis: 'stake' | 'payout';
    contract_type: 'CALL' | 'PUT' | 'CALLE' | 'PUTE';
    currency: string;
    duration: number;
    duration_unit: 'min' | 'h' | 'd' | 't' | 's';
    symbol: string;
    barrier?: string;
    product_type?: string;
}
export interface ProposalResponse {
    proposal: {
        ask_price: number;
        date_start: number;
        display_value: string;
        id: string;
        longcode: string;
        payout: number;
        spot: number;
        spot_time: number;
    };
    echo_req: ProposalRequest;
}
export interface BuyRequest {
    buy: string;
    price: number;
}
export interface BuyResponse {
    buy: {
        balance_after: number;
        buy_price: number;
        contract_id: number;
        longcode: string;
        payout: number;
        purchase_time: number;
        shortcode: string;
        start_time: number;
        transaction_id: number;
    };
}
export interface TradingSignal {
    direction: 'CALL' | 'PUT';
    confidence: number;
    timestamp: number;
    reason: string;
}
export interface TradeConfig {
    symbol: string;
    stake: number;
    currency: string;
    duration: number;
    durationUnit: 'min' | 'h' | 'd' | 't' | 's';
    contractType: 'CALL' | 'PUT' | 'CALLE' | 'PUTE';
    allowEquals?: boolean;
}
export interface RiskManagement {
    maxDailyLoss: number;
    maxConsecutiveLosses: number;
    stopLossPercentage: number;
    maxStakePercentage: number;
}
export interface BotConfig {
    trading: TradeConfig;
    risk: RiskManagement;
    strategy: {
        type: string;
        parameters: Record<string, unknown>;
    };
}
export interface TradeResult {
    id: string;
    symbol: string;
    contractType: string;
    stake: number;
    payout: number;
    profit: number;
    timestamp: number;
    status: 'won' | 'lost' | 'pending';
}
export interface BotStats {
    totalTrades: number;
    winRate: number;
    totalProfit: number;
    dailyProfit: number;
    consecutiveLosses: number;
    isActive: boolean;
    lastTradeTime: number;
}
export interface DerivAPIError {
    error: {
        code: string;
        message: string;
        details?: Record<string, unknown>;
    };
}
export interface WSMessage {
    msg_type: string;
    req_id?: number;
    [key: string]: unknown;
}
//# sourceMappingURL=index.d.ts.map