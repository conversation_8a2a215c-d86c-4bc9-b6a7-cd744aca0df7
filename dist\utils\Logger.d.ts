export declare class Logger {
    private static instance;
    private logger;
    private constructor();
    static getInstance(): Logger;
    private ensureLogsDirectory;
    info(message: string, meta?: Record<string, unknown>): void;
    warn(message: string, meta?: Record<string, unknown>): void;
    error(message: string, error?: Error | Record<string, unknown>): void;
    debug(message: string, meta?: Record<string, unknown>): void;
    trade(message: string, meta?: Record<string, unknown>): void;
    api(message: string, meta?: Record<string, unknown>): void;
    strategy(message: string, meta?: Record<string, unknown>): void;
    risk(message: string, meta?: Record<string, unknown>): void;
}
export declare class PerformanceMonitor {
    private static instance;
    private logger;
    private metrics;
    private constructor();
    static getInstance(): PerformanceMonitor;
    startTimer(operation: string): () => void;
    private recordMetric;
    getMetrics(): Record<string, {
        count: number;
        averageTime: number;
        minTime: number;
        maxTime: number;
        lastExecution: number;
    }>;
    logMetrics(): void;
    resetMetrics(): void;
}
//# sourceMappingURL=Logger.d.ts.map