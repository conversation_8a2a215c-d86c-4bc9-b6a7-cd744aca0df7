"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitor = exports.Logger = void 0;
const winston_1 = __importDefault(require("winston"));
const ConfigManager_1 = require("../config/ConfigManager");
class Logger {
    constructor() {
        const config = ConfigManager_1.ConfigManager.getInstance();
        const logLevel = config.getLogLevel();
        const environment = config.getEnvironment();
        this.logger = winston_1.default.createLogger({
            level: logLevel,
            format: winston_1.default.format.combine(winston_1.default.format.timestamp({
                format: 'YYYY-MM-DD HH:mm:ss',
            }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
            defaultMeta: { service: 'deriv-bot', environment },
            transports: [
                new winston_1.default.transports.File({
                    filename: 'logs/error.log',
                    level: 'error',
                    maxsize: 5242880,
                    maxFiles: 5,
                }),
                new winston_1.default.transports.File({
                    filename: 'logs/combined.log',
                    maxsize: 5242880,
                    maxFiles: 5,
                }),
                new winston_1.default.transports.File({
                    filename: 'logs/trading.log',
                    level: 'info',
                    maxsize: 5242880,
                    maxFiles: 10,
                    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
                        return `${timestamp} [${level.toUpperCase()}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
                    })),
                }),
            ],
        });
        if (environment !== 'production') {
            this.logger.add(new winston_1.default.transports.Console({
                format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
                    return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
                })),
            }));
        }
        this.ensureLogsDirectory();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    ensureLogsDirectory() {
        const fs = require('fs');
        const path = require('path');
        const logsDir = path.join(process.cwd(), 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
    }
    info(message, meta) {
        this.logger.info(message, meta);
    }
    warn(message, meta) {
        this.logger.warn(message, meta);
    }
    error(message, error) {
        if (error instanceof Error) {
            this.logger.error(message, { error: error.message, stack: error.stack });
        }
        else {
            this.logger.error(message, error);
        }
    }
    debug(message, meta) {
        this.logger.debug(message, meta);
    }
    trade(message, meta) {
        this.logger.info(`[TRADE] ${message}`, meta);
    }
    api(message, meta) {
        this.logger.debug(`[API] ${message}`, meta);
    }
    strategy(message, meta) {
        this.logger.info(`[STRATEGY] ${message}`, meta);
    }
    risk(message, meta) {
        this.logger.warn(`[RISK] ${message}`, meta);
    }
}
exports.Logger = Logger;
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.logger = Logger.getInstance();
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    startTimer(operation) {
        const startTime = Date.now();
        return () => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            this.recordMetric(operation, duration);
        };
    }
    recordMetric(operation, duration) {
        const existing = this.metrics.get(operation);
        if (existing) {
            existing.count++;
            existing.totalTime += duration;
            existing.minTime = Math.min(existing.minTime, duration);
            existing.maxTime = Math.max(existing.maxTime, duration);
            existing.lastExecution = Date.now();
        }
        else {
            this.metrics.set(operation, {
                count: 1,
                totalTime: duration,
                minTime: duration,
                maxTime: duration,
                lastExecution: Date.now(),
            });
        }
        if (duration > 5000) {
            this.logger.warn(`Slow operation detected: ${operation}`, {
                duration,
                operation,
            });
        }
    }
    getMetrics() {
        const result = {};
        for (const [operation, metrics] of this.metrics) {
            result[operation] = {
                count: metrics.count,
                averageTime: metrics.totalTime / metrics.count,
                minTime: metrics.minTime,
                maxTime: metrics.maxTime,
                lastExecution: metrics.lastExecution,
            };
        }
        return result;
    }
    logMetrics() {
        const metrics = this.getMetrics();
        this.logger.info('Performance metrics', { metrics });
    }
    resetMetrics() {
        this.metrics.clear();
        this.logger.info('Performance metrics reset');
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
//# sourceMappingURL=Logger.js.map