import React from 'react';
import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';
import { LogEntry } from '../types/trading';

interface ActivityLogProps {
  logs: LogEntry[];
  maxEntries?: number;
}

export const ActivityLog: React.FC<ActivityLogProps> = ({ logs, maxEntries = 50 }) => {
  const getLogIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'warn':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'info':
      default:
        return <Info className="w-4 h-4 text-blue-600" />;
    }
  };

  const getLogColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'success':
        return 'text-green-800 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-800 bg-red-50 border-red-200';
      case 'warn':
        return 'text-yellow-800 bg-yellow-50 border-yellow-200';
      case 'info':
      default:
        return 'text-blue-800 bg-blue-50 border-blue-200';
    }
  };

  const displayLogs = logs.slice(0, maxEntries);

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Activity Log</h3>
        <span className="text-sm text-gray-500">
          {logs.length} entries
        </span>
      </div>
      
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {displayLogs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Info className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No activity logs yet</p>
          </div>
        ) : (
          displayLogs.map((log) => (
            <div
              key={log.id}
              className={`flex items-start gap-3 p-3 rounded-lg border ${getLogColor(log.level)}`}
            >
              <div className="flex-shrink-0 mt-0.5">
                {getLogIcon(log.level)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">{log.message}</p>
                <p className="text-xs opacity-75 mt-1">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </p>
                {log.data && (
                  <pre className="text-xs mt-2 p-2 bg-black bg-opacity-10 rounded overflow-x-auto">
                    {JSON.stringify(log.data, null, 2)}
                  </pre>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
