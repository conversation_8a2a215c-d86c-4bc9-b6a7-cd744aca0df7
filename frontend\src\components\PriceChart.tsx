import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TickData } from '../types/trading';

interface PriceChartProps {
  tickData: TickData[];
  symbol: string;
}

export const PriceChart: React.FC<PriceChartProps> = ({ tickData, symbol }) => {
  const chartData = tickData.slice(-50).map((tick, index) => ({
    index,
    price: tick.quote,
    time: new Date(tick.timestamp).toLocaleTimeString(),
    timestamp: tick.timestamp
  }));

  const currentPrice = tickData[tickData.length - 1]?.quote || 0;
  const previousPrice = tickData[tickData.length - 2]?.quote || 0;
  const priceChange = currentPrice - previousPrice;
  const priceChangePercent = previousPrice ? (priceChange / previousPrice) * 100 : 0;

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {symbol} Price Chart
        </h3>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">
            {currentPrice.toFixed(2)}
          </div>
          <div className={`text-sm ${priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}
            ({priceChange >= 0 ? '+' : ''}{priceChangePercent.toFixed(2)}%)
          </div>
        </div>
      </div>
      
      <div className="h-64">
        {chartData.length > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                dataKey="index"
                tick={{ fontSize: 12 }}
                stroke="#6b7280"
              />
              <YAxis 
                domain={['dataMin - 0.5', 'dataMax + 0.5']}
                tick={{ fontSize: 12 }}
                stroke="#6b7280"
              />
              <Tooltip 
                labelFormatter={(value) => `Tick ${value}`}
                formatter={(value: number) => [value.toFixed(2), 'Price']}
                contentStyle={{
                  backgroundColor: '#ffffff',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '12px'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="price" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: '#3b82f6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">📈</div>
              <p>Waiting for price data...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
