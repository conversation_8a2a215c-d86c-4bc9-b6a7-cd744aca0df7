import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Target } from 'lucide-react';
import { DashboardStats } from '../types/trading';

interface StatsCardProps {
  stats: DashboardStats;
}

export const StatsCard: React.FC<StatsCardProps> = ({ stats }) => {
  const statItems = [
    {
      label: 'Total Trades',
      value: stats.totalTrades.toString(),
      icon: Target,
      color: 'text-blue-600'
    },
    {
      label: 'Win Rate',
      value: `${stats.winRate.toFixed(1)}%`,
      icon: stats.winRate >= 50 ? TrendingUp : TrendingDown,
      color: stats.winRate >= 50 ? 'text-green-600' : 'text-red-600'
    },
    {
      label: 'Total Profit',
      value: `$${stats.totalProfit.toFixed(2)}`,
      icon: DollarSign,
      color: stats.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'
    },
    {
      label: 'Today Profit',
      value: `$${stats.todayProfit.toFixed(2)}`,
      icon: DollarSign,
      color: stats.todayProfit >= 0 ? 'text-green-600' : 'text-red-600'
    }
  ];

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Trading Statistics</h3>
      
      <div className="grid grid-cols-2 gap-4">
        {statItems.map((item, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-gray-50 ${item.color}`}>
              <item.icon className="w-5 h-5" />
            </div>
            <div>
              <p className="text-sm text-gray-600">{item.label}</p>
              <p className={`text-lg font-semibold ${item.color}`}>
                {item.value}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      {stats.activeTrades > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <span className="font-medium">{stats.activeTrades}</span> active trade{stats.activeTrades !== 1 ? 's' : ''}
          </p>
        </div>
      )}
    </div>
  );
};
