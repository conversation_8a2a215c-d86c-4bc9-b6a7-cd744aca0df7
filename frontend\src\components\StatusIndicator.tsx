import React from 'react';
import { Circle } from 'lucide-react';

interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'trading';
  label: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, label }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          label: 'Online'
        };
      case 'trading':
        return {
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          label: 'Trading'
        };
      case 'offline':
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          label: 'Offline'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`status-indicator ${config.bgColor} ${config.textColor}`}>
      <Circle className={`w-2 h-2 mr-1.5 ${config.color} fill-current`} />
      {label}: {config.label}
    </div>
  );
};
