import React from 'react';
import { Play, Square, Settings } from 'lucide-react';
import { BotStatus } from '../types/trading';

interface TradingControlsProps {
  botStatus: BotStatus;
  onStart: () => void;
  onStop: () => void;
  onSettings: () => void;
}

export const TradingControls: React.FC<TradingControlsProps> = ({
  botStatus,
  onStart,
  onStop,
  onSettings
}) => {
  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Trading Controls</h3>
      
      <div className="flex gap-3 mb-4">
        {!botStatus.isRunning ? (
          <button
            onClick={onStart}
            className="btn-success flex items-center gap-2"
            disabled={!botStatus.isConnected}
          >
            <Play className="w-4 h-4" />
            Start Trading
          </button>
        ) : (
          <button
            onClick={onStop}
            className="btn-danger flex items-center gap-2"
          >
            <Square className="w-4 h-4" />
            Stop Trading
          </button>
        )}
        
        <button
          onClick={onSettings}
          className="btn-primary flex items-center gap-2"
        >
          <Settings className="w-4 h-4" />
          Settings
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-600">Symbol:</span>
          <span className="ml-2 font-medium">{botStatus.symbol}</span>
        </div>
        <div>
          <span className="text-gray-600">Balance:</span>
          <span className="ml-2 font-medium text-green-600">
            ${botStatus.balance.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};
