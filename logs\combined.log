{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751072800280},"timestamp":"2025-06-27 21:06:40","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751072800306},"timestamp":"2025-06-27 21:06:40","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:06:40"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751072900994},"timestamp":"2025-06-27 21:08:20","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751072901033},"timestamp":"2025-06-27 21:08:21","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:08:21"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751072964099},"timestamp":"2025-06-27 21:09:24","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751072964136},"timestamp":"2025-06-27 21:09:24","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:09:24"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073038214},"timestamp":"2025-06-27 21:10:38","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073038256},"timestamp":"2025-06-27 21:10:38","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:10:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073136423},"timestamp":"2025-06-27 21:12:16","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073136498},"timestamp":"2025-06-27 21:12:16","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:12:16"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073409816},"timestamp":"2025-06-27 21:16:49","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073409878},"timestamp":"2025-06-27 21:16:49","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:16:49"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073518426},"timestamp":"2025-06-27 21:18:38","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073518488},"timestamp":"2025-06-27 21:18:38","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:18:38"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073599499},"timestamp":"2025-06-27 21:19:59","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073599555},"timestamp":"2025-06-27 21:19:59","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:19:59"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073718684},"timestamp":"2025-06-27 21:21:58","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073718729},"timestamp":"2025-06-27 21:21:58","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:21:58"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:00","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:00","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:00"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:01","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:01"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:02","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:02"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:03","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:03","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:03","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:03"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:04","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:22:04"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:22:06"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:22:06","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:22:06"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073821908},"timestamp":"2025-06-27 21:23:41","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073822016},"timestamp":"2025-06-27 21:23:42","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:23:42"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751073851438},"timestamp":"2025-06-27 21:24:11","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751073851492},"timestamp":"2025-06-27 21:24:11","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:24:11"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:12","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:12"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:13","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:13"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:14","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:14","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:14"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:15","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:15"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:16","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:16","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:16"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:17","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:24:17"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:24:18"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:24:18","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:24:18"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074060922},"timestamp":"2025-06-27 21:27:40","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751074060971},"timestamp":"2025-06-27 21:27:40","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:27:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:42","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:42","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:42"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:44","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:44"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Subscribed to R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:47","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:47"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:48","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:48","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:48"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:49","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:27:49"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:27:52"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:27:52","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:27:52"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:29:04"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:29:07"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:29:07","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:29:07"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:29:54"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:29:56"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:29:56","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:29:56"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:31:27"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:31:29"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:31:29","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:31:29"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:32:14"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:32:16"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:32:16","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:32:16"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:32:57"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074377654},"timestamp":"2025-06-27 21:32:57","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074377762},"timestamp":"2025-06-27 21:32:57","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074377871},"timestamp":"2025-06-27 21:32:57","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074377980},"timestamp":"2025-06-27 21:32:57","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074378092},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.5653196878944156,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.8561)","timestamp":1751074378122},"timestamp":"2025-06-27 21:32:58","trend":"down","volatility":0.8560792624201992}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7905138339920921,"service":"deriv-bot","signal":{"confidence":0.7162055335968369,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.9341)","timestamp":1751074378199},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":0.934052444015542}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7889546351084784,"service":"deriv-bot","signal":{"confidence":0.7155818540433914,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0123)","timestamp":1751074378310},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":1.0123419544782797}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7874015748031609,"service":"deriv-bot","signal":{"confidence":0.7149606299212644,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0909)","timestamp":1751074378419},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":1.090879689710282}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7858546168958714,"service":"deriv-bot","signal":{"confidence":0.7143418467583486,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.1696)","timestamp":1751074378527},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":1.1696156471456312}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":3.4007302051495736,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 3.40% price increase","timestamp":1751074378638},"timestamp":"2025-06-27 21:32:58","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-2.617153152955052,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -2.62% price decrease (high volatility: 1.2835)","timestamp":1751074379134},"timestamp":"2025-06-27 21:32:59","trend":"down","volatility":1.283476919205205}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:32:59"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:32:59"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:32:59","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:32:59"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:32:59"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074424122},"timestamp":"2025-06-27 21:33:44","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751074424158},"timestamp":"2025-06-27 21:33:44","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:33:44"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074425999},"timestamp":"2025-06-27 21:33:45","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074426108},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074426216},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074426327},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074426436},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7905138339920921,"service":"deriv-bot","signal":{"confidence":0.895256916996046,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase","timestamp":1751074426546},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.47382572902933173}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7889546351084784,"service":"deriv-bot","signal":{"confidence":0.7155818540433914,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.5474)","timestamp":1751074426656},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.5473923437956628}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7874015748031609,"service":"deriv-bot","signal":{"confidence":0.7149606299212644,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6225)","timestamp":1751074426766},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.6225479375935965}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7858546168958714,"service":"deriv-bot","signal":{"confidence":0.7143418467583486,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6988)","timestamp":1751074426876},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.6987800019734169}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.1603414833875108,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 2.16% price increase","timestamp":1751074426984},"timestamp":"2025-06-27 21:33:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.1810096836783248,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.18% price decrease (high volatility: 0.7106)","timestamp":1751074427454},"timestamp":"2025-06-27 21:33:47","trend":"down","volatility":0.7105786958541008}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:47","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:47"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:48","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"Subscribed to R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.5,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074428981},"timestamp":"2025-06-27 21:33:48","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[TRADE] Executing trade","service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074428981},"stake":10,"symbol":"R_100","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4955134596211366,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074429093},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4910536779324057,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074429201},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":0.42426406871192446}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4866204162537164,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074429311},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4822134387351777,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.48% price increase","timestamp":1751074429420},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.44615361062818,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.45% price decrease (high volatility: 0.9365)","timestamp":1751074429451},"timestamp":"2025-06-27 21:33:49","trend":"down","volatility":0.9364905126473044}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.178781925343814,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0509)","timestamp":1751074429530},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":1.0508699672642758}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.175318315377084,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1664)","timestamp":1751074429640},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":1.1663793962161835}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.171874999999989,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.2827)","timestamp":1751074429748},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":1.2827135704190689}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.1684518013631966,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3997)","timestamp":1751074429859},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":1.3996668573474365}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":4.166506212829552,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 4.17% price increase","timestamp":1751074429965},"timestamp":"2025-06-27 21:33:49","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-3.269856744540637,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -3.27% price decrease (high volatility: 1.6743)","timestamp":1751074430462},"timestamp":"2025-06-27 21:33:50","trend":"down","volatility":1.6742775088850281}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:50","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:50"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:51","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:51","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:51"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074431426},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074431488},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074431551},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074431614},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074431677},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9900990099009901,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074431739},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9881422924901186,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074431801},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9861932938856016,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074431863},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.984251968503937,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751074431926},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9823182711198428,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751074431989},"timestamp":"2025-06-27 21:33:51","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-2.6027310374994626,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -2.60% price decrease (high volatility: 1.2777)","timestamp":1751074432128},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":1.2776649043937252}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:52","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:33:52"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45454545454545453,"service":"deriv-bot","signal":{"confidence":0.7272727272727273,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751074432563},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4549590536851683,"service":"deriv-bot","signal":{"confidence":0.7274795268425842,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751074432626},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45537340619307837,"service":"deriv-bot","signal":{"confidence":0.7276867030965392,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432689},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4557885141294439,"service":"deriv-bot","signal":{"confidence":0.727894257064722,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432751},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45620437956204385,"service":"deriv-bot","signal":{"confidence":0.7281021897810219,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432813},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45662100456621,"service":"deriv-bot","signal":{"confidence":0.728310502283105,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432875},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4570383912248629,"service":"deriv-bot","signal":{"confidence":0.7285191956124315,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432938},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4574565416285453,"service":"deriv-bot","signal":{"confidence":0.7287282708142726,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074432999},"timestamp":"2025-06-27 21:33:52","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4578754578754579,"service":"deriv-bot","signal":{"confidence":0.728937728937729,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433062},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.458295142071494,"service":"deriv-bot","signal":{"confidence":0.729147571035747,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433125},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45871559633027525,"service":"deriv-bot","signal":{"confidence":0.7293577981651376,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433187},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4591368227731864,"service":"deriv-bot","signal":{"confidence":0.7295684113865932,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433248},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-8.741698487275148,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -8.74% price decrease (high volatility: 3.7057)","timestamp":1751074433263},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":3.705736686768613}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46168051708217916,"service":"deriv-bot","signal":{"confidence":0.7308402585410896,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433621},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4621072088724584,"service":"deriv-bot","signal":{"confidence":0.7310536044362292,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433682},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46253469010175763,"service":"deriv-bot","signal":{"confidence":0.7312673450508789,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074433745},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-21.296296296296298,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)","timestamp":1751074433808},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":9.100549433962765}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.852641334569043,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)","timestamp":1751074433870},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":11.023611023616533}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.408163265306122,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)","timestamp":1751074433932},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":10.903210536351208}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-19.96285979572888,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)","timestamp":1751074433995},"timestamp":"2025-06-27 21:33:53","trend":"down","volatility":8.809086218218095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.3529411764705883,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)","timestamp":1751074434120},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.34192037470726,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)","timestamp":1751074434183},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.331002331002331,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)","timestamp":1751074434246},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":15.820984849874971,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 15.82% price increase (high volatility: 5.0709)","timestamp":1751074434277},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":5.070876559478327}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8475750577367305,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.85% price increase (high volatility: 4.9114)","timestamp":1751074434308},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":4.9113913402488025}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8390804597701083,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.84% price increase (high volatility: 4.7519)","timestamp":1751074434369},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":4.7519407311829065}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8306636155606342,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.83% price increase (high volatility: 4.5925)","timestamp":1751074434431},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":4.592528337232045}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2675736961451247,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)","timestamp":1751074434620},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2573363431151243,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)","timestamp":1751074434682},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.247191011235955,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)","timestamp":1751074434744},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2371364653243844,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)","timestamp":1751074434807},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2271714922048997,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)","timestamp":1751074434869},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2172949002217295,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)","timestamp":1751074434932},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.207505518763797,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)","timestamp":1751074434995},"timestamp":"2025-06-27 21:33:54","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":9.611108188367988,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 9.61% price increase (high volatility: 3.1242)","timestamp":1751074435289},"timestamp":"2025-06-27 21:33:55","trend":"up","volatility":3.1241561072668786}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:33:55"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:55"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:33:55","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:33:55"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:33:55"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074598490},"timestamp":"2025-06-27 21:36:38","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751074598534},"timestamp":"2025-06-27 21:36:38","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:36:38"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:39","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:40","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:40"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074600693},"timestamp":"2025-06-27 21:36:40","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074600802},"timestamp":"2025-06-27 21:36:40","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074600911},"timestamp":"2025-06-27 21:36:40","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074601019},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074601128},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.8754982227552366,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.88% price decrease (high volatility: 0.9784)","timestamp":1751074601161},"timestamp":"2025-06-27 21:36:41","trend":"down","volatility":0.9783617661842965}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7905138339920921,"service":"deriv-bot","signal":{"confidence":0.7162055335968369,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0568)","timestamp":1751074601238},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":1.0567989168475498}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7889546351084784,"service":"deriv-bot","signal":{"confidence":0.7155818540433914,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.1355)","timestamp":1751074601348},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":1.1354541627776322}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7874015748031609,"service":"deriv-bot","signal":{"confidence":0.7149606299212644,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.2143)","timestamp":1751074601457},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":1.2142851233906948}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7858546168958714,"service":"deriv-bot","signal":{"confidence":0.7143418467583486,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 1.2933)","timestamp":1751074601566},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":1.2932596668909346}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":3.727587273645895,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 3.73% price increase","timestamp":1751074601674},"timestamp":"2025-06-27 21:36:41","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.992518647736017,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.99% price decrease (high volatility: 1.0325)","timestamp":1751074602169},"timestamp":"2025-06-27 21:36:42","trend":"down","volatility":1.0325031995653404}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:42","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:42"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:43","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"Subscribed to R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.5,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074603665},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[TRADE] Executing trade","service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074603665},"stake":10,"symbol":"R_100","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.5,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074603667},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4955134596211366,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074603772},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4955134596211366,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751074603773},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119285}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4910536779324057,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074603881},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.42426406871192446}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4910536779324057,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074603882},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.42426406871192446}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4866204162537164,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074603993},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4866204162537164,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751074603995},"timestamp":"2025-06-27 21:36:43","trend":"up","volatility":0.4242640687119285}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4822134387351777,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.48% price increase","timestamp":1751074604101},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4822134387351777,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.48% price increase","timestamp":1751074604102},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.8043478561278945,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.80% price decrease (high volatility: 1.0753)","timestamp":1751074604132},"timestamp":"2025-06-27 21:36:44","trend":"down","volatility":1.0752632018970827}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.8043478561278945,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.80% price decrease (high volatility: 1.0753)","timestamp":1751074604133},"timestamp":"2025-06-27 21:36:44","trend":"down","volatility":1.0752632018970827}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.178781925343814,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1910)","timestamp":1751074604211},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.1909687688831594}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.178781925343814,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1910)","timestamp":1751074604213},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.1909687688831594}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.175318315377084,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.3074)","timestamp":1751074604320},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.3074487613501986}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.175318315377084,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.3074)","timestamp":1751074604321},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.3074487613501986}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.171874999999989,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4245)","timestamp":1751074604430},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.4245132216505634}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.171874999999989,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4245)","timestamp":1751074604432},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.4245132216505634}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.1684518013631966,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.5420)","timestamp":1751074604541},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.5420290443951996}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.1684518013631966,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.5420)","timestamp":1751074604542},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":1.5420290443951996}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":4.546480705433136,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 4.55% price increase","timestamp":1751074604652},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":4.546480705433136,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 4.55% price increase","timestamp":1751074604652},"timestamp":"2025-06-27 21:36:44","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-2.93828403456182,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -2.94% price decrease (high volatility: 1.5401)","timestamp":1751074605144},"timestamp":"2025-06-27 21:36:45","trend":"down","volatility":1.540074951266232}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-2.93828403456182,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -2.94% price decrease (high volatility: 1.5401)","timestamp":1751074605146},"timestamp":"2025-06-27 21:36:45","trend":"down","volatility":1.540074951266232}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:45","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:45"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074606109},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074606171},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751074606232},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074606295},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074606356},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9900990099009901,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074606418},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9881422924901186,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074606480},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9861932938856016,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751074606544},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.984251968503937,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751074606605},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9823182711198428,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751074606668},"timestamp":"2025-06-27 21:36:46","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.6485363004413083,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.65% price decrease (high volatility: 0.8952)","timestamp":1751074606809},"timestamp":"2025-06-27 21:36:46","trend":"down","volatility":0.8952293924085647}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:46","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:36:46"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45454545454545453,"service":"deriv-bot","signal":{"confidence":0.7272727272727273,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751074607243},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4549590536851683,"service":"deriv-bot","signal":{"confidence":0.7274795268425842,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751074607304},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45537340619307837,"service":"deriv-bot","signal":{"confidence":0.7276867030965392,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607366},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4557885141294439,"service":"deriv-bot","signal":{"confidence":0.727894257064722,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607429},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45620437956204385,"service":"deriv-bot","signal":{"confidence":0.7281021897810219,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607491},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45662100456621,"service":"deriv-bot","signal":{"confidence":0.728310502283105,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607553},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4570383912248629,"service":"deriv-bot","signal":{"confidence":0.7285191956124315,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607616},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4574565416285453,"service":"deriv-bot","signal":{"confidence":0.7287282708142726,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607679},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4578754578754579,"service":"deriv-bot","signal":{"confidence":0.728937728937729,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607741},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.458295142071494,"service":"deriv-bot","signal":{"confidence":0.729147571035747,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607802},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45871559633027525,"service":"deriv-bot","signal":{"confidence":0.7293577981651376,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607864},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4591368227731864,"service":"deriv-bot","signal":{"confidence":0.7295684113865932,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074607926},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-7.485144791331229,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -7.49% price decrease (high volatility: 3.1591)","timestamp":1751074607941},"timestamp":"2025-06-27 21:36:47","trend":"down","volatility":3.159118130033135}
{"environment":"demo","level":"info","longMA":108.02374416446878,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":108.1170774978021,"prevShortMA":106.65123249340631,"service":"deriv-bot","shortMA":108.1,"signal":{"confidence":0.7070591735290264,"direction":"CALL","reason":"Bullish MA crossover: Short MA (108.1000) > Long MA (108.0237)","timestamp":1751074608236},"timestamp":"2025-06-27 21:36:48"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46168051708217916,"service":"deriv-bot","signal":{"confidence":0.7308402585410896,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074608299},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4621072088724584,"service":"deriv-bot","signal":{"confidence":0.7310536044362292,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074608364},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46253469010175763,"service":"deriv-bot","signal":{"confidence":0.7312673450508789,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751074608423},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-21.296296296296298,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)","timestamp":1751074608485},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":9.100549433962765}
{"environment":"demo","level":"info","longMA":106.15041083113543,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":107.74374416446878,"prevShortMA":107.8,"service":"deriv-bot","shortMA":103.2,"signal":{"confidence":0.9,"direction":"PUT","reason":"Bearish MA crossover: Short MA (103.2000) < Long MA (106.1504)","timestamp":1751074608487},"timestamp":"2025-06-27 21:36:48"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.852641334569043,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)","timestamp":1751074608547},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":11.023611023616533}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.408163265306122,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)","timestamp":1751074608609},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":10.903210536351208}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-19.96285979572888,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)","timestamp":1751074608670},"timestamp":"2025-06-27 21:36:48","trend":"down","volatility":8.809086218218095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.3529411764705883,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)","timestamp":1751074608795},"timestamp":"2025-06-27 21:36:48","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.34192037470726,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)","timestamp":1751074608857},"timestamp":"2025-06-27 21:36:48","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.331002331002331,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)","timestamp":1751074608919},"timestamp":"2025-06-27 21:36:48","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":17.860128147100944,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 17.86% price increase (high volatility: 5.7720)","timestamp":1751074608951},"timestamp":"2025-06-27 21:36:48","trend":"up","volatility":5.772048762224236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8475750577367305,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.85% price increase (high volatility: 5.6124)","timestamp":1751074608981},"timestamp":"2025-06-27 21:36:48","trend":"up","volatility":5.6124443707047815}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8390804597701083,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.84% price increase (high volatility: 5.4529)","timestamp":1751074609045},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":5.452863166724182}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8306636155606342,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.83% price increase (high volatility: 5.2933)","timestamp":1751074609106},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":5.293307247437914}
{"environment":"demo","level":"info","longMA":90.8863620308534,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":92.11302869752006,"prevShortMA":91.03908609256021,"service":"deriv-bot","shortMA":91.3590860925602,"signal":{"confidence":0.7520126508690403,"direction":"CALL","reason":"Bullish MA crossover: Short MA (91.3591) > Long MA (90.8864)","timestamp":1751074609168},"timestamp":"2025-06-27 21:36:49"}
{"environment":"demo","level":"info","longMA":89.69302869752006,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":90.8863620308534,"prevShortMA":91.3590860925602,"service":"deriv-bot","shortMA":89.00000000000001,"signal":{"confidence":0.7772667293750568,"direction":"PUT","reason":"Bearish MA crossover: Short MA (89.0000) < Long MA (89.6930)","timestamp":1751074609229},"timestamp":"2025-06-27 21:36:49"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2675736961451247,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)","timestamp":1751074609293},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","longMA":88.53302869752007,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":89.69302869752006,"prevShortMA":89.00000000000001,"service":"deriv-bot","shortMA":89.4,"signal":{"confidence":0.7979263123869865,"direction":"CALL","reason":"Bullish MA crossover: Short MA (89.4000) > Long MA (88.5330)","timestamp":1751074609295},"timestamp":"2025-06-27 21:36:49"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2573363431151243,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)","timestamp":1751074609355},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.247191011235955,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)","timestamp":1751074609417},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2371364653243844,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)","timestamp":1751074609480},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2271714922048997,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)","timestamp":1751074609544},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2172949002217295,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)","timestamp":1751074609605},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.207505518763797,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)","timestamp":1751074609666},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":11.863214122989612,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 11.86% price increase (high volatility: 3.9386)","timestamp":1751074609963},"timestamp":"2025-06-27 21:36:49","trend":"up","volatility":3.9385745060789294}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:36:50","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:36:50"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075074243},"timestamp":"2025-06-27 21:44:34","trend":"up","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.0040160642570282,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"PUT","reason":"Downward trend detected with -1.00% price decrease","timestamp":1751075074270},"timestamp":"2025-06-27 21:44:34","trend":"down","volatility":0.2872281323269024}
{"environment":"demo","level":"info","message":"Strategy reset","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:34","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:34","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075075417},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075075527},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075075636},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075075746},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075075855},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.5471891247661251,"service":"deriv-bot","signal":{"confidence":0.7735945623830626,"direction":"PUT","reason":"Downward trend detected with -0.55% price decrease","timestamp":1751075075871},"timestamp":"2025-06-27 21:44:35","trend":"down","volatility":0.4661493691314268}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7905138339920921,"service":"deriv-bot","signal":{"confidence":0.7162055335968369,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.5395)","timestamp":1751075075963},"timestamp":"2025-06-27 21:44:35","trend":"up","volatility":0.5395049020782942}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7889546351084784,"service":"deriv-bot","signal":{"confidence":0.7155818540433914,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6145)","timestamp":1751075076073},"timestamp":"2025-06-27 21:44:36","trend":"up","volatility":0.6145208250266165}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7874015748031609,"service":"deriv-bot","signal":{"confidence":0.7149606299212644,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6907)","timestamp":1751075076181},"timestamp":"2025-06-27 21:44:36","trend":"up","volatility":0.6906563178718321}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.7858546168958714,"service":"deriv-bot","signal":{"confidence":0.7143418467583486,"direction":"CALL","reason":"Upward trend detected with 0.79% price increase (high volatility: 0.7676)","timestamp":1751075076290},"timestamp":"2025-06-27 21:44:36","trend":"up","volatility":0.7675783050876032}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.342183516069904,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 2.34% price increase","timestamp":1751075076399},"timestamp":"2025-06-27 21:44:36","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.5887750261101123,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.59% price decrease (high volatility: 0.8715)","timestamp":1751075076883},"timestamp":"2025-06-27 21:44:36","trend":"down","volatility":0.8714800776610401}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:37","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:37","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"Subscribed to R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:37"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.5,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751075078395},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[TRADE] Executing trade","service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751075078395},"stake":10,"symbol":"R_100","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.5,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751075078397},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4955134596211366,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751075078504},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4955134596211366,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.50% price increase","timestamp":1751075078505},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119285}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4910536779324057,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751075078613},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.42426406871192446}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4910536779324057,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751075078614},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.42426406871192446}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4866204162537164,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751075078723},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119285}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4866204162537164,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.49% price increase","timestamp":1751075078724},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119285}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4822134387351777,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.48% price increase","timestamp":1751075078830},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.4822134387351777,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.48% price increase","timestamp":1751075078831},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.5675876828518387,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.9833)","timestamp":1751075078862},"timestamp":"2025-06-27 21:44:38","trend":"down","volatility":0.9833214102728257}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.5675876828518387,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.9833)","timestamp":1751075078863},"timestamp":"2025-06-27 21:44:38","trend":"down","volatility":0.9833214102728257}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.178781925343814,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0982)","timestamp":1751075078941},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":1.0982106991456702}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.178781925343814,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0982)","timestamp":1751075078943},"timestamp":"2025-06-27 21:44:38","trend":"up","volatility":1.0982106991456702}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.175318315377084,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.2141)","timestamp":1751075079051},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.2140891579843316}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.175318315377084,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.18% price increase (high volatility: 1.2141)","timestamp":1751075079052},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.2140891579843316}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.171874999999989,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3307)","timestamp":1751075079161},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.3306983983428338}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.171874999999989,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3307)","timestamp":1751075079162},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.3306983983428338}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.1684518013631966,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4479)","timestamp":1751075079270},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.4478618619085422}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.1684518013631966,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4479)","timestamp":1751075079271},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":1.4478618619085422}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":4.295014320483959,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 4.30% price increase","timestamp":1751075079379},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":0.4242640687119305}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":4.295014320483959,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 4.30% price increase","timestamp":1751075079380},"timestamp":"2025-06-27 21:44:39","trend":"up","volatility":0.4242640687119305}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-3.5373391661289064,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -3.54% price decrease (high volatility: 1.7828)","timestamp":1751075079864},"timestamp":"2025-06-27 21:44:39","trend":"down","volatility":1.7828061019420411}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-3.5373391661289064,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -3.54% price decrease (high volatility: 1.7828)","timestamp":1751075079865},"timestamp":"2025-06-27 21:44:39","trend":"down","volatility":1.7828061019420411}
{"dailyProfit":-2,"environment":"demo","level":"warn","maxDailyLoss":1,"message":"[RISK] Daily loss limit reached","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"dailyProfit":-2,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:39","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:39"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:40","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:40","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:40"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075080813},"timestamp":"2025-06-27 21:44:40","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.998003992015968,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075080876},"timestamp":"2025-06-27 21:44:40","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9960159362549801,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 1.00% price increase","timestamp":1751075080939},"timestamp":"2025-06-27 21:44:40","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9940357852882704,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075081001},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.992063492063492,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075081064},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9900990099009901,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075081127},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9881422924901186,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075081189},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9861932938856016,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.99% price increase","timestamp":1751075081252},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.28284271247462306}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.984251968503937,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751075081316},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474619}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":0.9823182711198428,"service":"deriv-bot","signal":{"confidence":0.9,"direction":"CALL","reason":"Upward trend detected with 0.98% price increase","timestamp":1751075081379},"timestamp":"2025-06-27 21:44:41","trend":"up","volatility":0.282842712474617}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-1.2588536999174391,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -1.26% price decrease (high volatility: 0.7411)","timestamp":1751075081520},"timestamp":"2025-06-27 21:44:41","trend":"down","volatility":0.7411089854836259}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:41","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Trading is disabled. Bot will only monitor market data.","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Starting monitoring mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"Monitoring R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 21:44:41"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45454545454545453,"service":"deriv-bot","signal":{"confidence":0.7272727272727273,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751075081958},"timestamp":"2025-06-27 21:44:41","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4549590536851683,"service":"deriv-bot","signal":{"confidence":0.7274795268425842,"direction":"PUT","reason":"Downward trend detected with -0.45% price decrease","timestamp":1751075082020},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45537340619307837,"service":"deriv-bot","signal":{"confidence":0.7276867030965392,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082082},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4557885141294439,"service":"deriv-bot","signal":{"confidence":0.727894257064722,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082145},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45620437956204385,"service":"deriv-bot","signal":{"confidence":0.7281021897810219,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082208},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45662100456621,"service":"deriv-bot","signal":{"confidence":0.728310502283105,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082269},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4570383912248629,"service":"deriv-bot","signal":{"confidence":0.7285191956124315,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082331},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4574565416285453,"service":"deriv-bot","signal":{"confidence":0.7287282708142726,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082393},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4578754578754579,"service":"deriv-bot","signal":{"confidence":0.728937728937729,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082457},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.458295142071494,"service":"deriv-bot","signal":{"confidence":0.729147571035747,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082517},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.45871559633027525,"service":"deriv-bot","signal":{"confidence":0.7293577981651376,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082580},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373115}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4591368227731864,"service":"deriv-bot","signal":{"confidence":0.7295684113865932,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075082642},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-8.470085732826137,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -8.47% price decrease (high volatility: 3.5876)","timestamp":1751075082658},"timestamp":"2025-06-27 21:44:42","trend":"down","volatility":3.587575280720178}
{"environment":"demo","level":"info","longMA":107.95230311484569,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":108.04563644817901,"prevShortMA":106.43690934453703,"service":"deriv-bot","shortMA":108.1,"signal":{"confidence":0.7136816798616307,"direction":"CALL","reason":"Bullish MA crossover: Short MA (108.1000) > Long MA (107.9523)","timestamp":1751075082954},"timestamp":"2025-06-27 21:44:42"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46168051708217916,"service":"deriv-bot","signal":{"confidence":0.7308402585410896,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075083016},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":0.1414213562373095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.4621072088724584,"service":"deriv-bot","signal":{"confidence":0.7310536044362292,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075083078},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":0.1414213562373075}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-0.46253469010175763,"service":"deriv-bot","signal":{"confidence":0.7312673450508789,"direction":"PUT","reason":"Downward trend detected with -0.46% price decrease","timestamp":1751075083139},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":0.14142135623731153}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-21.296296296296298,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)","timestamp":1751075083201},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":9.100549433962765}
{"environment":"demo","level":"info","longMA":106.07896978151236,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":107.67230311484566,"prevShortMA":107.8,"service":"deriv-bot","shortMA":103.2,"signal":{"confidence":0.9,"direction":"PUT","reason":"Bearish MA crossover: Short MA (103.2000) < Long MA (106.0790)","timestamp":1751075083202},"timestamp":"2025-06-27 21:44:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.852641334569043,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)","timestamp":1751075083263},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":11.023611023616533}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-20.408163265306122,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)","timestamp":1751075083324},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":10.903210536351208}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":-19.96285979572888,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"PUT","reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)","timestamp":1751075083387},"timestamp":"2025-06-27 21:44:43","trend":"down","volatility":8.809086218218095}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.3529411764705883,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)","timestamp":1751075083513},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.34192037470726,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)","timestamp":1751075083574},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.331002331002331,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)","timestamp":1751075083637},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":15.257714189451042,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 15.26% price increase (high volatility: 4.8773)","timestamp":1751075083669},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":4.877290078093286}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8475750577367305,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.85% price increase (high volatility: 4.7178)","timestamp":1751075083699},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":4.717847321931893}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8390804597701083,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.84% price increase (high volatility: 4.5584)","timestamp":1751075083761},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":4.558443616000165}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":1.8306636155606342,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 1.83% price increase (high volatility: 4.3991)","timestamp":1751075083825},"timestamp":"2025-06-27 21:44:43","trend":"up","volatility":4.399083205333288}
{"environment":"demo","level":"info","longMA":90.73680997542046,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":91.9634766420871,"prevShortMA":90.59042992626136,"service":"deriv-bot","shortMA":90.91042992626134,"signal":{"confidence":0.7191344561141083,"direction":"CALL","reason":"Bullish MA crossover: Short MA (90.9104) > Long MA (90.7368)","timestamp":1751075083888},"timestamp":"2025-06-27 21:44:43"}
{"environment":"demo","level":"info","longMA":89.54347664208713,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":90.73680997542046,"prevShortMA":90.91042992626134,"service":"deriv-bot","shortMA":89.00000000000001,"signal":{"confidence":0.7606941636027196,"direction":"PUT","reason":"Bearish MA crossover: Short MA (89.0000) < Long MA (89.5435)","timestamp":1751075083950},"timestamp":"2025-06-27 21:44:43"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2675736961451247,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)","timestamp":1751075084013},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","longMA":88.38347664208713,"message":"[STRATEGY] MA Crossover signal generated","prevLongMA":89.54347664208713,"prevShortMA":89.00000000000001,"service":"deriv-bot","shortMA":89.4,"signal":{"confidence":0.8150128277969118,"direction":"CALL","reason":"Bullish MA crossover: Short MA (89.4000) > Long MA (88.3835)","timestamp":1751075084014},"timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2573363431151243,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)","timestamp":1751075084077},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.247191011235955,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)","timestamp":1751075084139},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2371364653243844,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)","timestamp":1751075084201},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2271714922048997,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)","timestamp":1751075084264},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.565685424949238}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.2172949002217295,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)","timestamp":1751075084327},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.56568542494924}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":2.207505518763797,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)","timestamp":1751075084389},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":0.565685424949236}
{"environment":"demo","level":"info","message":"[STRATEGY] Signal generated","priceChange":8.250123809834129,"service":"deriv-bot","signal":{"confidence":0.7200000000000001,"direction":"CALL","reason":"Upward trend detected with 8.25% price increase (high volatility: 2.6336)","timestamp":1751075084682},"timestamp":"2025-06-27 21:44:44","trend":"up","volatility":2.6335989861187543}
{"environment":"demo","level":"info","message":"Stopping Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"dailyProfit":0,"environment":"demo","level":"info","message":"Bot Statistics","service":"deriv-bot","stats":{"consecutiveLosses":0,"dailyProfit":0,"isActive":false,"lastTradeTime":0,"totalProfit":0,"totalTrades":0,"winRate":0},"timestamp":"2025-06-27 21:44:44","totalTrades":0}
{"environment":"demo","level":"info","message":"Bot stopped","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"warn","message":"API disconnected","service":"deriv-bot","timestamp":"2025-06-27 21:44:44"}
{"environment":"demo","level":"info","message":"🚀 Starting Deriv Bot Demo with your API key...","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"📊 Configuration loaded:","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- Environment: demo","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- Trading Enabled: true","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- Symbol: R_100","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- Stake: 1 USD","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- Strategy: simple_trend","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"🎯 Starting bot...","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:46:07"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:46:37"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 21:46:37"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:47:07"}
{"environment":"demo","level":"info","message":"🔍 Testing Deriv API Connection...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"📊 Using configuration:","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"- App ID: 1089","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"- API URL: wss://ws.binaryws.com/websockets/v3","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"🔌 Connecting to Deriv API...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"📊 Testing basic API calls...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"📋 Getting active symbols...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"✅ Active symbols received: 85 symbols","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","level":"info","message":"💰 Getting account balance...","service":"deriv-bot","timestamp":"2025-06-27 21:49:59"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Get balance failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:50:29"}
{"environment":"demo","level":"info","message":"📈 Testing tick subscription...","service":"deriv-bot","timestamp":"2025-06-27 21:50:29"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Tick subscription failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:50:59"}
{"environment":"demo","level":"info","message":"🧹 Cleaning up...","service":"deriv-bot","timestamp":"2025-06-27 21:50:59"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:50:59"}
{"environment":"demo","level":"info","message":"✅ Connection test completed","service":"deriv-bot","timestamp":"2025-06-27 21:51:00"}
{"environment":"demo","level":"info","message":"🔍 Testing Deriv API Connection...","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"📊 Using configuration:","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"- App ID: 1089","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"- API URL: wss://ws.binaryws.com/websockets/v3","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"🔌 Connecting to Deriv API...","service":"deriv-bot","timestamp":"2025-06-27 21:52:33"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:52:34"}
{"environment":"demo","level":"info","message":"🔐 Authorizing with API token...","service":"deriv-bot","timestamp":"2025-06-27 21:52:34"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Authorization failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:53:04"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:54:48"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:54:49"}
{"environment":"demo","level":"info","message":"🔍 Simple WebSocket Test...","service":"deriv-bot","timestamp":"2025-06-27 21:56:01"}
{"environment":"demo","level":"info","message":"📡 Connecting to: wss://ws.binaryws.com/websockets/v3?app_id=1089&l=en","service":"deriv-bot","timestamp":"2025-06-27 21:56:01"}
{"environment":"demo","level":"info","message":"✅ WebSocket connected","service":"deriv-bot","timestamp":"2025-06-27 21:56:01"}
{"environment":"demo","level":"info","message":"📋 Testing active symbols...","service":"deriv-bot","timestamp":"2025-06-27 21:56:01"}
{"environment":"demo","level":"info","message":"🔐 Testing authorization...","service":"deriv-bot","timestamp":"2025-06-27 21:56:03"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:56:29"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:56:30"}
{"environment":"demo","level":"warn","message":"⏰ Test timeout, closing...","service":"deriv-bot","timestamp":"2025-06-27 21:56:31"}
{"environment":"demo","level":"info","message":"🔚 WebSocket closed","service":"deriv-bot","timestamp":"2025-06-27 21:56:31"}
{"environment":"demo","level":"info","message":"🔍 Simple WebSocket Test...","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"📡 Connecting to: wss://ws.binaryws.com/websockets/v3?app_id=1089&l=en","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"✅ WebSocket connected","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"📋 Testing active symbols...","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"📤 Sending: {\"active_symbols\":\"brief\",\"product_type\":\"basic\",\"req_id\":1}","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"📨 Received message: {\n  \"active_symbols\": [\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"AUD Basket\",\n      \"display_order\": 30,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"baskets\",\n      \"subgroup_display_name\": \"Baskets\",\n      \"submarket\": \"forex_basket\",\n      \"submarket_display_name\": \"Forex Basket\",\n      \"symbol\": \"WLDAUD\",\n      \"symbol_type\": \"forex_basket\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"AUD/CAD\",\n      \"display_order\": 10,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxAUDCAD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"AUD/CHF\",\n      \"display_order\": 16,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxAUDCHF\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"AUD/JPY\",\n      \"display_order\": 1,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxAUDJPY\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"AUD/NZD\",\n      \"display_order\": 18,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxAUDNZD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"AUD/USD\",\n      \"display_order\": 2,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxAUDUSD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Australia 200\",\n      \"display_order\": 4,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"asia_oceania_OTC\",\n      \"submarket_display_name\": \"Asian indices\",\n      \"symbol\": \"OTC_AS51\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"BTC/USD\",\n      \"display_order\": 0,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"cryptocurrency\",\n      \"market_display_name\": \"Cryptocurrencies\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"non_stable_coin\",\n      \"submarket_display_name\": \"Cryptocurrencies\",\n      \"symbol\": \"cryBTCUSD\",\n      \"symbol_type\": \"cryptocurrency\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Bear Market Index\",\n      \"display_order\": 10,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_daily\",\n      \"submarket_display_name\": \"Daily Reset Indices\",\n      \"symbol\": \"RDBEAR\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Boom 300 Index\",\n      \"display_order\": 26,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"BOOM300N\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Boom 500 Index\",\n      \"display_order\": 18,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"BOOM500\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Boom 600 Index\",\n      \"display_order\": 33,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"BOOM600\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Boom 900 Index\",\n      \"display_order\": 36,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"BOOM900\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Boom 1000 Index\",\n      \"display_order\": 21,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"BOOM1000\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Bull Market Index\",\n      \"display_order\": 12,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_daily\",\n      \"submarket_display_name\": \"Daily Reset Indices\",\n      \"symbol\": \"RDBULL\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Crash 300 Index\",\n      \"display_order\": 28,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"CRASH300N\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Crash 500 Index\",\n      \"display_order\": 19,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"CRASH500\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Crash 600 Index\",\n      \"display_order\": 31,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"CRASH600\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Crash 900 Index\",\n      \"display_order\": 35,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"CRASH900\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Crash 1000 Index\",\n      \"display_order\": 22,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"crash_index\",\n      \"submarket_display_name\": \"Crash/Boom Indices\",\n      \"symbol\": \"CRASH1000\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"ETH/USD\",\n      \"display_order\": 1,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"cryptocurrency\",\n      \"market_display_name\": \"Cryptocurrencies\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"non_stable_coin\",\n      \"submarket_display_name\": \"Cryptocurrencies\",\n      \"symbol\": \"cryETHUSD\",\n      \"symbol_type\": \"cryptocurrency\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"EUR Basket\",\n      \"display_order\": 37,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"baskets\",\n      \"subgroup_display_name\": \"Baskets\",\n      \"submarket\": \"forex_basket\",\n      \"submarket_display_name\": \"Forex Basket\",\n      \"symbol\": \"WLDEUR\",\n      \"symbol_type\": \"forex_basket\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/AUD\",\n      \"display_order\": 6,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURAUD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/CAD\",\n      \"display_order\": 14,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURCAD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/CHF\",\n      \"display_order\": 13,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURCHF\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/GBP\",\n      \"display_order\": 9,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURGBP\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/JPY\",\n      \"display_order\": 8,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURJPY\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/NZD\",\n      \"display_order\": 19,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxEURNZD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"EUR/USD\",\n      \"display_order\": 0,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxEURUSD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Euro 50\",\n      \"display_order\": 8,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_SX5E\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"France 40\",\n      \"display_order\": 9,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_FCHI\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"GBP Basket\",\n      \"display_order\": 38,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"baskets\",\n      \"subgroup_display_name\": \"Baskets\",\n      \"submarket\": \"forex_basket\",\n      \"submarket_display_name\": \"Forex Basket\",\n      \"symbol\": \"WLDGBP\",\n      \"symbol_type\": \"forex_basket\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/AUD\",\n      \"display_order\": 11,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxGBPAUD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/CAD\",\n      \"display_order\": 17,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxGBPCAD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/CHF\",\n      \"display_order\": 20,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxGBPCHF\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/JPY\",\n      \"display_order\": 5,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxGBPJPY\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/NOK\",\n      \"display_order\": 27,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxGBPNOK\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/NZD\",\n      \"display_order\": 21,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxGBPNZD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"GBP/USD\",\n      \"display_order\": 4,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxGBPUSD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Germany 40\",\n      \"display_order\": 7,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_GDAXI\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Gold Basket\",\n      \"display_order\": 32,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"baskets\",\n      \"subgroup_display_name\": \"Baskets\",\n      \"submarket\": \"commodity_basket\",\n      \"submarket_display_name\": \"Commodities Basket\",\n      \"symbol\": \"WLDXAU\",\n      \"symbol_type\": \"commodity_basket\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Gold/USD\",\n      \"display_order\": 0,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"commodities\",\n      \"market_display_name\": \"Commodities\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"metals\",\n      \"submarket_display_name\": \"Metals\",\n      \"symbol\": \"frxXAUUSD\",\n      \"symbol_type\": \"commodities\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Hong Kong 50\",\n      \"display_order\": 10,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"asia_oceania_OTC\",\n      \"submarket_display_name\": \"Asian indices\",\n      \"symbol\": \"OTC_HSI\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Japan 225\",\n      \"display_order\": 6,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"asia_oceania_OTC\",\n      \"submarket_display_name\": \"Asian indices\",\n      \"symbol\": \"OTC_N225\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Jump 10 Index\",\n      \"display_order\": 11,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"jump_index\",\n      \"submarket_display_name\": \"Jump Indices\",\n      \"symbol\": \"JD10\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Jump 25 Index\",\n      \"display_order\": 16,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"jump_index\",\n      \"submarket_display_name\": \"Jump Indices\",\n      \"symbol\": \"JD25\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Jump 50 Index\",\n      \"display_order\": 17,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"jump_index\",\n      \"submarket_display_name\": \"Jump Indices\",\n      \"symbol\": \"JD50\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Jump 75 Index\",\n      \"display_order\": 14,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"jump_index\",\n      \"submarket_display_name\": \"Jump Indices\",\n      \"symbol\": \"JD75\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Jump 100 Index\",\n      \"display_order\": 13,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"jump_index\",\n      \"submarket_display_name\": \"Jump Indices\",\n      \"symbol\": \"JD100\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"NZD/JPY\",\n      \"display_order\": 23,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxNZDJPY\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"NZD/USD\",\n      \"display_order\": 15,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxNZDUSD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Netherlands 25\",\n      \"display_order\": 11,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_AEX\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Palladium/USD\",\n      \"display_order\": 2,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"commodities\",\n      \"market_display_name\": \"Commodities\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"metals\",\n      \"submarket_display_name\": \"Metals\",\n      \"symbol\": \"frxXPDUSD\",\n      \"symbol_type\": \"commodities\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Platinum/USD\",\n      \"display_order\": 3,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"commodities\",\n      \"market_display_name\": \"Commodities\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"metals\",\n      \"submarket_display_name\": \"Metals\",\n      \"symbol\": \"frxXPTUSD\",\n      \"symbol_type\": \"commodities\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Silver/USD\",\n      \"display_order\": 1,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"commodities\",\n      \"market_display_name\": \"Commodities\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"metals\",\n      \"submarket_display_name\": \"Metals\",\n      \"symbol\": \"frxXAGUSD\",\n      \"symbol_type\": \"commodities\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Step Index 100\",\n      \"display_order\": 15,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.1,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"step_index\",\n      \"submarket_display_name\": \"Step Indices\",\n      \"symbol\": \"stpRNG\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Step Index 200\",\n      \"display_order\": 23,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.1,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"step_index\",\n      \"submarket_display_name\": \"Step Indices\",\n      \"symbol\": \"stpRNG2\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Step Index 300\",\n      \"display_order\": 24,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.1,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"step_index\",\n      \"submarket_display_name\": \"Step Indices\",\n      \"symbol\": \"stpRNG3\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Step Index 400\",\n      \"display_order\": 25,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.1,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"step_index\",\n      \"submarket_display_name\": \"Step Indices\",\n      \"symbol\": \"stpRNG4\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Step Index 500\",\n      \"display_order\": 20,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.1,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"step_index\",\n      \"submarket_display_name\": \"Step Indices\",\n      \"symbol\": \"stpRNG5\",\n      \"symbol_type\": \"\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Swiss 20\",\n      \"display_order\": 5,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_SSMI\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"UK 100\",\n      \"display_order\": 3,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"europe_OTC\",\n      \"submarket_display_name\": \"European indices\",\n      \"symbol\": \"OTC_FTSE\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"US 500\",\n      \"display_order\": 0,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"americas_OTC\",\n      \"submarket_display_name\": \"American indices\",\n      \"symbol\": \"OTC_SPC\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"US Tech 100\",\n      \"display_order\": 1,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"americas_OTC\",\n      \"submarket_display_name\": \"American indices\",\n      \"symbol\": \"OTC_NDX\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"USD Basket\",\n      \"display_order\": 34,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"baskets\",\n      \"subgroup_display_name\": \"Baskets\",\n      \"submarket\": \"forex_basket\",\n      \"submarket_display_name\": \"Forex Basket\",\n      \"symbol\": \"WLDUSD\",\n      \"symbol_type\": \"forex_basket\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/CAD\",\n      \"display_order\": 7,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxUSDCAD\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/CHF\",\n      \"display_order\": 12,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxUSDCHF\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/JPY\",\n      \"display_order\": 3,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"major_pairs\",\n      \"submarket_display_name\": \"Major Pairs\",\n      \"symbol\": \"frxUSDJPY\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/MXN\",\n      \"display_order\": 22,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxUSDMXN\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/NOK\",\n      \"display_order\": 26,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxUSDNOK\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/PLN\",\n      \"display_order\": 24,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxUSDPLN\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"USD/SEK\",\n      \"display_order\": 25,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"forex\",\n      \"market_display_name\": \"Forex\",\n      \"pip\": 0.00001,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"minor_pairs\",\n      \"submarket_display_name\": \"Minor Pairs\",\n      \"symbol\": \"frxUSDSEK\",\n      \"symbol_type\": \"forex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 10 (1s) Index\",\n      \"display_order\": 2,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ10V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 10 Index\",\n      \"display_order\": 3,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"R_10\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 25 (1s) Index\",\n      \"display_order\": 5,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ25V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 25 Index\",\n      \"display_order\": 7,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"R_25\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 50 (1s) Index\",\n      \"display_order\": 9,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ50V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 50 Index\",\n      \"display_order\": 4,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"R_50\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 75 (1s) Index\",\n      \"display_order\": 6,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ75V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 75 Index\",\n      \"display_order\": 8,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.0001,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"R_75\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 100 (1s) Index\",\n      \"display_order\": 1,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ100V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Volatility 100 Index\",\n      \"display_order\": 0,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"R_100\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Volatility 150 (1s) Index\",\n      \"display_order\": 27,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ150V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 0,\n      \"display_name\": \"Volatility 250 (1s) Index\",\n      \"display_order\": 29,\n      \"exchange_is_open\": 1,\n      \"is_trading_suspended\": 0,\n      \"market\": \"synthetic_index\",\n      \"market_display_name\": \"Derived\",\n      \"pip\": 0.01,\n      \"subgroup\": \"synthetics\",\n      \"subgroup_display_name\": \"Synthetics\",\n      \"submarket\": \"random_index\",\n      \"submarket_display_name\": \"Continuous Indices\",\n      \"symbol\": \"1HZ250V\",\n      \"symbol_type\": \"stockindex\"\n    },\n    {\n      \"allow_forward_starting\": 1,\n      \"display_name\": \"Wall Street 30\",\n      \"display_order\": 2,\n      \"exchange_is_open\": 0,\n      \"is_trading_suspended\": 0,\n      \"market\": \"indices\",\n      \"market_display_name\": \"Stock Indices\",\n      \"pip\": 0.01,\n      \"subgroup\": \"none\",\n      \"subgroup_display_name\": \"None\",\n      \"submarket\": \"americas_OTC\",\n      \"submarket_display_name\": \"American indices\",\n      \"symbol\": \"OTC_DJI\",\n      \"symbol_type\": \"stockindex\"\n    }\n  ],\n  \"echo_req\": {\n    \"active_symbols\": \"brief\",\n    \"product_type\": \"basic\",\n    \"req_id\": 1\n  },\n  \"msg_type\": \"active_symbols\",\n  \"req_id\": 1\n}","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"✅ Active symbols: 85 symbols received","service":"deriv-bot","timestamp":"2025-06-27 21:57:13"}
{"environment":"demo","level":"info","message":"🔐 Testing authorization...","service":"deriv-bot","timestamp":"2025-06-27 21:57:15"}
{"environment":"demo","level":"info","message":"📤 Sending: {\"authorize\":\"hEMSCZEtzrg5BzA\",\"req_id\":2}","service":"deriv-bot","timestamp":"2025-06-27 21:57:15"}
{"environment":"demo","level":"info","message":"📨 Received message: {\n  \"authorize\": {\n    \"account_list\": [\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"CR\",\n        \"created_at\": **********,\n        \"currency\": \"USD\",\n        \"currency_type\": \"fiat\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 0,\n        \"landing_company_name\": \"svg\",\n        \"linked_to\": [],\n        \"loginid\": \"CR2481365\"\n      },\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"CR\",\n        \"created_at\": **********,\n        \"currency\": \"eUSDT\",\n        \"currency_type\": \"crypto\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 0,\n        \"landing_company_name\": \"svg\",\n        \"linked_to\": [],\n        \"loginid\": \"CR5869675\"\n      },\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"CR\",\n        \"created_at\": **********,\n        \"currency\": \"tUSDT\",\n        \"currency_type\": \"crypto\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 0,\n        \"landing_company_name\": \"svg\",\n        \"linked_to\": [],\n        \"loginid\": \"CR6683184\"\n      },\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"CR\",\n        \"created_at\": **********,\n        \"currency\": \"USDC\",\n        \"currency_type\": \"crypto\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 0,\n        \"landing_company_name\": \"svg\",\n        \"linked_to\": [],\n        \"loginid\": \"CR6748108\"\n      },\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"CR\",\n        \"created_at\": **********,\n        \"currency\": \"ETH\",\n        \"currency_type\": \"crypto\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 0,\n        \"landing_company_name\": \"svg\",\n        \"linked_to\": [],\n        \"loginid\": \"CR6766403\"\n      },\n      {\n        \"account_category\": \"trading\",\n        \"account_type\": \"binary\",\n        \"broker\": \"VRTC\",\n        \"created_at\": **********,\n        \"currency\": \"USD\",\n        \"currency_type\": \"fiat\",\n        \"is_disabled\": 0,\n        \"is_virtual\": 1,\n        \"landing_company_name\": \"virtual\",\n        \"linked_to\": [],\n        \"loginid\": \"VRTC3871036\"\n      }\n    ],\n    \"balance\": 24856.64,\n    \"country\": \"ke\",\n    \"currency\": \"USD\",\n    \"email\": \"<EMAIL>\",\n    \"fullname\": \" Samuel Mweni\",\n    \"is_virtual\": 1,\n    \"landing_company_fullname\": \"Deriv Limited\",\n    \"landing_company_name\": \"virtual\",\n    \"linked_to\": [],\n    \"local_currencies\": {\n      \"KES\": {\n        \"fractional_digits\": 2\n      }\n    },\n    \"loginid\": \"VRTC3871036\",\n    \"preferred_language\": \"EN\",\n    \"scopes\": [\n      \"payments\",\n      \"read\",\n      \"trade\",\n      \"trading_information\"\n    ],\n    \"upgradeable_landing_companies\": [\n      \"svg\"\n    ],\n    \"user_id\": 7983887\n  },\n  \"echo_req\": {\n    \"authorize\": \"<not shown>\",\n    \"req_id\": 2\n  },\n  \"msg_type\": \"authorize\",\n  \"req_id\": 2\n}","service":"deriv-bot","timestamp":"2025-06-27 21:57:15"}
{"environment":"demo","level":"info","message":"✅ Authorization successful!","service":"deriv-bot","timestamp":"2025-06-27 21:57:15"}
{"environment":"demo","level":"info","message":"Account info: {\n  \"account_list\": [\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"CR\",\n      \"created_at\": **********,\n      \"currency\": \"USD\",\n      \"currency_type\": \"fiat\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 0,\n      \"landing_company_name\": \"svg\",\n      \"linked_to\": [],\n      \"loginid\": \"CR2481365\"\n    },\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"CR\",\n      \"created_at\": **********,\n      \"currency\": \"eUSDT\",\n      \"currency_type\": \"crypto\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 0,\n      \"landing_company_name\": \"svg\",\n      \"linked_to\": [],\n      \"loginid\": \"CR5869675\"\n    },\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"CR\",\n      \"created_at\": **********,\n      \"currency\": \"tUSDT\",\n      \"currency_type\": \"crypto\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 0,\n      \"landing_company_name\": \"svg\",\n      \"linked_to\": [],\n      \"loginid\": \"CR6683184\"\n    },\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"CR\",\n      \"created_at\": **********,\n      \"currency\": \"USDC\",\n      \"currency_type\": \"crypto\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 0,\n      \"landing_company_name\": \"svg\",\n      \"linked_to\": [],\n      \"loginid\": \"CR6748108\"\n    },\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"CR\",\n      \"created_at\": **********,\n      \"currency\": \"ETH\",\n      \"currency_type\": \"crypto\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 0,\n      \"landing_company_name\": \"svg\",\n      \"linked_to\": [],\n      \"loginid\": \"CR6766403\"\n    },\n    {\n      \"account_category\": \"trading\",\n      \"account_type\": \"binary\",\n      \"broker\": \"VRTC\",\n      \"created_at\": **********,\n      \"currency\": \"USD\",\n      \"currency_type\": \"fiat\",\n      \"is_disabled\": 0,\n      \"is_virtual\": 1,\n      \"landing_company_name\": \"virtual\",\n      \"linked_to\": [],\n      \"loginid\": \"VRTC3871036\"\n    }\n  ],\n  \"balance\": 24856.64,\n  \"country\": \"ke\",\n  \"currency\": \"USD\",\n  \"email\": \"<EMAIL>\",\n  \"fullname\": \" Samuel Mweni\",\n  \"is_virtual\": 1,\n  \"landing_company_fullname\": \"Deriv Limited\",\n  \"landing_company_name\": \"virtual\",\n  \"linked_to\": [],\n  \"local_currencies\": {\n    \"KES\": {\n      \"fractional_digits\": 2\n    }\n  },\n  \"loginid\": \"VRTC3871036\",\n  \"preferred_language\": \"EN\",\n  \"scopes\": [\n    \"payments\",\n    \"read\",\n    \"trade\",\n    \"trading_information\"\n  ],\n  \"upgradeable_landing_companies\": [\n    \"svg\"\n  ],\n  \"user_id\": 7983887\n}","service":"deriv-bot","timestamp":"2025-06-27 21:57:15"}
{"environment":"demo","level":"info","message":"💰 Testing balance...","service":"deriv-bot","timestamp":"2025-06-27 21:57:16"}
{"environment":"demo","level":"info","message":"📨 Received message: {\n  \"balance\": {\n    \"balance\": 24856.64,\n    \"currency\": \"USD\",\n    \"loginid\": \"VRTC3871036\"\n  },\n  \"echo_req\": {\n    \"balance\": 1,\n    \"req_id\": 3\n  },\n  \"msg_type\": \"balance\",\n  \"req_id\": 3\n}","service":"deriv-bot","timestamp":"2025-06-27 21:57:16"}
{"environment":"demo","level":"info","message":"✅ Balance received: {\n  \"balance\": 24856.64,\n  \"currency\": \"USD\",\n  \"loginid\": \"VRTC3871036\"\n}","service":"deriv-bot","timestamp":"2025-06-27 21:57:16"}
{"environment":"demo","level":"info","message":"🔚 Closing connection...","service":"deriv-bot","timestamp":"2025-06-27 21:57:17"}
{"environment":"demo","level":"info","message":"🔚 WebSocket closed","service":"deriv-bot","timestamp":"2025-06-27 21:57:17"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:58:10"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:58:11"}
{"environment":"demo","level":"info","message":"🚀 Starting Deriv Bot Demo with your API key...","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"📊 Configuration loaded:","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- Environment: demo","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- Trading Enabled: true","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- Symbol: R_100","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- Stake: 1 USD","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- Strategy: simple_trend","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"🎯 Starting bot...","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:59:14"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:59:44"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:59:51"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 21:59:52"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:01:32"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:01:34"}
{"environment":"demo","level":"info","message":"🚀 Starting Deriv Bot Demo with your API key...","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"📊 Configuration loaded:","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- Environment: demo","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- Trading Enabled: true","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- Symbol: R_100","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- Stake: 1 USD","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- Strategy: simple_trend","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"🎯 Starting bot...","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:01:40"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 22:02:11"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 22:02:11"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 22:02:41"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:03:14"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:03:15"}
{"environment":"demo","level":"info","message":"🚀 Starting Deriv Bot Demo with your API key...","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"📊 Configuration loaded:","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- Environment: demo","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- Trading Enabled: true","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- Symbol: R_100","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- Stake: 1 USD","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- Strategy: simple_trend","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"- API Token: hEMSCZEt...","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"🎯 Starting bot...","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 22:03:53"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:03:54"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:03:54"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 22:03:54"}
{"environment":"demo","level":"info","message":"Subscribed to R_100 ticks","service":"deriv-bot","timestamp":"2025-06-27 22:03:56"}
{"environment":"demo","level":"info","message":"✅ Bot started successfully! Monitoring for signals...","service":"deriv-bot","timestamp":"2025-06-27 22:03:56"}
{"environment":"demo","level":"info","message":"💡 Press Ctrl+C to stop the bot","service":"deriv-bot","timestamp":"2025-06-27 22:03:56"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:04:55"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:04:56"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:06:36"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:06:37"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:08:17"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:08:18"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:09:58"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 22:10:00"}
{"environment":"demo","level":"info","message":"🌐 Starting Deriv Bot Web Interface...","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"Web bot manager started successfully","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"🎯 Web interface started successfully!","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"📊 Dashboard available at: http://localhost:5173","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"🔌 WebSocket server running on: http://localhost:3001","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"💡 Use the web interface to start/stop the bot","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"🛑 Press Ctrl+C to stop the web server","service":"deriv-bot","timestamp":"2025-06-27 22:46:18"}
{"environment":"demo","level":"info","message":"🛑 Shutting down web interface...","service":"deriv-bot","timestamp":"2025-06-27 23:07:58"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-27 23:08:13"}
{"environment":"demo","level":"info","message":"🤖 Deriv Rise/Fall Trading Bot Starting...","service":"deriv-bot","timestamp":"2025-06-27 23:08:13"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-27 23:08:13"}
{"environment":"demo","level":"info","message":"Configuration loaded successfully","service":"deriv-bot","strategy":"simple_trend","symbol":"R_100","timestamp":"2025-06-27 23:08:13","tradingEnabled":true}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 23:08:13"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:08:14"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:08:14"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 23:08:44"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 23:08:44"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 23:09:14"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-27 23:09:26"}
{"environment":"demo","level":"info","message":"🤖 Deriv Rise/Fall Trading Bot Starting...","service":"deriv-bot","timestamp":"2025-06-27 23:09:26"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-27 23:09:26"}
{"environment":"demo","level":"info","message":"Configuration loaded successfully","service":"deriv-bot","strategy":"simple_trend","symbol":"R_100","timestamp":"2025-06-27 23:09:26","tradingEnabled":true}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-27 23:09:26"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:09:26"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:09:27"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 23:09:57"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-27 23:09:57"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 23:10:27"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:30:42"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-27 23:30:44"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:27:08"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:09"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:27:09"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:09"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:11"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:27:11"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:11"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:15"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:27:15"}
{"environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","level":"error","message":"❌ API Error:","service":"deriv-bot","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","timestamp":"2025-06-28 00:27:15"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:27:24"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:29:04"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:29:05"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:30:45"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:30:46"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:32:26"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:32:28"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:34:08"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:34:09"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:35:49"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:35:50"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:37:30"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:37:31"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:39:11"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:39:12"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:40:52"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:40:54"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:42:34"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:42:35"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:44:15"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:44:16"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:45:56"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:45:57"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:47:37"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:47:38"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:49:18"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:49:19"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:51:00"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:51:01"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:52:41"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:52:42"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:54:22"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:54:23"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:56:03"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:56:04"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:57:44"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:57:46"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:59:26"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 00:59:27"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:01:07"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:01:08"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:02:48"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:02:49"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:04:29"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:04:30"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:06:10"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:06:11"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:07:51"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:07:53"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:09:33"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:09:34"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:11:14"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:11:15"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:12:55"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:12:56"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:14:36"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:14:38"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:16:18"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:16:19"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:17:59"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:18:00"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:19:40"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:19:41"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:21:21"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:21:22"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:23:02"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:23:04"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:24:44"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:24:45"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:26:25"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:26:26"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:28:06"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:28:08"}
{"environment":"demo","level":"info","message":"❌ Disconnected from Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:29:48"}
{"environment":"demo","level":"info","message":"✅ Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 01:29:49"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","level":"info","message":"🤖 Deriv Rise/Fall Trading Bot Starting...","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","level":"info","message":"Configuration loaded successfully","service":"deriv-bot","strategy":"simple_trend","symbol":"R_100","timestamp":"2025-06-28 18:23:18","tradingEnabled":true}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:23:18"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:23:48"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-28 18:23:48"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:24:18"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:24:26"}
{"environment":"demo","level":"info","message":"🤖 Deriv Rise/Fall Trading Bot Starting...","service":"deriv-bot","timestamp":"2025-06-28 18:24:26"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:24:26"}
{"environment":"demo","level":"info","message":"Configuration loaded successfully","service":"deriv-bot","strategy":"simple_trend","symbol":"R_100","timestamp":"2025-06-28 18:24:26","tradingEnabled":true}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-28 18:24:26"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:24:27"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:24:27"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:24:57"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-28 18:24:57"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:25:27"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:25:37"}
{"environment":"demo","level":"info","message":"🤖 Deriv Rise/Fall Trading Bot Starting...","service":"deriv-bot","timestamp":"2025-06-28 18:25:37"}
{"environment":"demo","level":"info","message":"==================================================","service":"deriv-bot","timestamp":"2025-06-28 18:25:37"}
{"environment":"demo","level":"info","message":"Configuration loaded successfully","service":"deriv-bot","strategy":"simple_trend","symbol":"R_100","timestamp":"2025-06-28 18:25:37","tradingEnabled":true}
{"environment":"demo","level":"info","message":"Starting Deriv Bot...","service":"deriv-bot","timestamp":"2025-06-28 18:25:37"}
{"environment":"demo","level":"info","message":"Connected to Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:25:37"}
{"environment":"demo","level":"info","message":"Authorized with Deriv API","service":"deriv-bot","timestamp":"2025-06-28 18:25:38"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:26:08"}
{"environment":"demo","level":"info","message":"Starting trading mode","service":"deriv-bot","symbol":"R_100","timestamp":"2025-06-28 18:26:08"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-28 18:26:38"}
