{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.authorize is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)","timestamp":"2025-06-27 21:18:40"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"this.api.subscribeTicks is not a function","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)","timestamp":"2025-06-27 21:20:01"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:33:45"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:33:48"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:36:39"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:36:43"}
{"environment":"demo","error":"Mock connection error","level":"error","message":"API error","service":"deriv-bot","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)","timestamp":"2025-06-27 21:44:34"}
{"environment":"demo","error":"this.api.getContract is not a function","level":"error","message":"Failed to execute trade","service":"deriv-bot","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)","timestamp":"2025-06-27 21:44:38"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:46:37"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:47:07"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Get balance failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:50:29"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Tick subscription failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:50:59"}
{"environment":"demo","error":"Request timeout","level":"error","message":"❌ Authorization failed:","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:53:04"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 21:59:44"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to get balance","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 22:02:11"}
{"environment":"demo","error":"Request timeout","level":"error","message":"Failed to start bot","service":"deriv-bot","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)","timestamp":"2025-06-27 22:02:41"}
