2025-06-27 21:06:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751072800280,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:06:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751072800306,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:06:40 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:08:20 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751072900994,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:08:21 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751072901033,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:08:21 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:09:24 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751072964099,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:09:24 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751072964136,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:09:24 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:10:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073038214,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:10:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073038256,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:10:38 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:12:16 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073136423,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:12:16 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073136498,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:12:16 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:16:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073409816,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:16:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073409878,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:16:49 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073518426,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:18:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073518488,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:18:38 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)"}
2025-06-27 21:18:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:18:40 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.authorize is not a function","stack":"TypeError: this.api.authorize is not a function\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:70:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)"}
2025-06-27 21:19:59 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073599499,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:19:59 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073599555,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:19:59 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:63:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:101:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:123:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:159:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:179:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:204:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:236:7)"}
2025-06-27 21:20:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:20:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:20:01 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"this.api.subscribeTicks is not a function","stack":"TypeError: this.api.subscribeTicks is not a function\n    at DerivBot.startMonitoring (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:141:20)\n    at DerivBot.start (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:82:20)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:266:7)"}
2025-06-27 21:21:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073718684,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:21:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073718729,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:21:58 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:00 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:00 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:00 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:00 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:00 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:00 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:01 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:01 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:01 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:02 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:02 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:02 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:22:03 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:03 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:03 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:03 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:03 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:03 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:03 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:04 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:04 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:22:04 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:06 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:22:06 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:22:06 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:23:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073821908,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:23:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073822016,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:23:42 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:11 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751073851438,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:24:11 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751073851492,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:24:11 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:12 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:12 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:12 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:12 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:13 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:13 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:13 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:14 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:14 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:14 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:14 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:14 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:24:15 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:15 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:15 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:16 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:16 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:16 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:16 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:16 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:17 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:17 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:24:17 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:18 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:24:18 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:24:18 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074060922,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:27:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751074060971,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:27:40 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:42 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:42 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:42 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:42 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:42 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:42 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:44 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:44 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:44 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:45 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:45 [INFO]: Subscribed to R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:27:47 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:47 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:47 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:48 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:48 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:48 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:48 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:48 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:49 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:49 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:27:49 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:52 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:27:52 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:27:52 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:04 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:04 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:04 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:04 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:04 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:29:04 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:07 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:07 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:29:07 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:54 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:54 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:54 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:54 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:54 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:29:54 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:56 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:29:56 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:29:56 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:27 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:27 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:27 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:27 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:27 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:31:27 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:29 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:31:29 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:31:29 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:14 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:14 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:14 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:14 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:14 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:32:14 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:16 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:16 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:32:16 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:32:57 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:57 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074377654,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:32:57 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074377762,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:32:57 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074377871,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:32:57 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074377980,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074378092,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074378122,"reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.8561)"},"trend":"down","priceChange":-1.5653196878944156,"volatility":0.8560792624201992}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7162055335968369,"timestamp":1751074378199,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.9341)"},"trend":"up","priceChange":0.7905138339920921,"volatility":0.934052444015542}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7155818540433914,"timestamp":1751074378310,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0123)"},"trend":"up","priceChange":0.7889546351084784,"volatility":1.0123419544782797}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7149606299212644,"timestamp":1751074378419,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0909)"},"trend":"up","priceChange":0.7874015748031609,"volatility":1.090879689710282}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7143418467583486,"timestamp":1751074378527,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.1696)"},"trend":"up","priceChange":0.7858546168958714,"volatility":1.1696156471456312}
2025-06-27 21:32:58 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074378638,"reason":"Upward trend detected with 3.40% price increase"},"trend":"up","priceChange":3.4007302051495736,"volatility":0.282842712474617}
2025-06-27 21:32:59 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074379134,"reason":"Downward trend detected with -2.62% price decrease (high volatility: 1.2835)"},"trend":"down","priceChange":-2.617153152955052,"volatility":1.283476919205205}
2025-06-27 21:32:59 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:59 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:59 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:32:59 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:32:59 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074424122,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:33:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751074424158,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:33:44 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [ERROR]: API error {"service":"deriv-bot","environment":"demo","error":"Mock connection error","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)"}
2025-06-27 21:33:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:45 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074425999,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074426108,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074426216,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074426327,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074426436,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.895256916996046,"timestamp":1751074426546,"reason":"Upward trend detected with 0.79% price increase"},"trend":"up","priceChange":0.7905138339920921,"volatility":0.47382572902933173}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7155818540433914,"timestamp":1751074426656,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.5474)"},"trend":"up","priceChange":0.7889546351084784,"volatility":0.5473923437956628}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7149606299212644,"timestamp":1751074426766,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6225)"},"trend":"up","priceChange":0.7874015748031609,"volatility":0.6225479375935965}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7143418467583486,"timestamp":1751074426876,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6988)"},"trend":"up","priceChange":0.7858546168958714,"volatility":0.6987800019734169}
2025-06-27 21:33:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074426984,"reason":"Upward trend detected with 2.16% price increase"},"trend":"up","priceChange":2.1603414833875108,"volatility":0.282842712474617}
2025-06-27 21:33:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074427454,"reason":"Downward trend detected with -1.18% price decrease (high volatility: 0.7106)"},"trend":"down","priceChange":-1.1810096836783248,"volatility":0.7105786958541008}
2025-06-27 21:33:47 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:47 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:47 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:47 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:48 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:48 [INFO]: Subscribed to R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074428981,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.5,"volatility":0.4242640687119305}
2025-06-27 21:33:48 [INFO]: [TRADE] Executing trade {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074428981,"reason":"Upward trend detected with 1.50% price increase"},"stake":10,"symbol":"R_100"}
2025-06-27 21:33:48 [ERROR]: Failed to execute trade {"service":"deriv-bot","environment":"demo","error":"this.api.getContract is not a function","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)"}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074429093,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.4955134596211366,"volatility":0.4242640687119285}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074429201,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4910536779324057,"volatility":0.42426406871192446}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074429311,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4866204162537164,"volatility":0.4242640687119285}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074429420,"reason":"Upward trend detected with 1.48% price increase"},"trend":"up","priceChange":1.4822134387351777,"volatility":0.4242640687119305}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074429451,"reason":"Downward trend detected with -1.45% price decrease (high volatility: 0.9365)"},"trend":"down","priceChange":-1.44615361062818,"volatility":0.9364905126473044}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074429530,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0509)"},"trend":"up","priceChange":1.178781925343814,"volatility":1.0508699672642758}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074429640,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1664)"},"trend":"up","priceChange":1.175318315377084,"volatility":1.1663793962161835}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074429748,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.2827)"},"trend":"up","priceChange":1.171874999999989,"volatility":1.2827135704190689}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074429859,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3997)"},"trend":"up","priceChange":1.1684518013631966,"volatility":1.3996668573474365}
2025-06-27 21:33:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074429965,"reason":"Upward trend detected with 4.17% price increase"},"trend":"up","priceChange":4.166506212829552,"volatility":0.4242640687119305}
2025-06-27 21:33:50 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074430462,"reason":"Downward trend detected with -3.27% price decrease (high volatility: 1.6743)"},"trend":"down","priceChange":-3.269856744540637,"volatility":1.6742775088850281}
2025-06-27 21:33:50 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:33:50 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:50 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:50 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:51 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:51 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:51 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:51 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431426,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431488,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431551,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431614,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431677,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431739,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9900990099009901,"volatility":0.282842712474617}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431801,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9881422924901186,"volatility":0.282842712474619}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431863,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9861932938856016,"volatility":0.28284271247462306}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431926,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.984251968503937,"volatility":0.282842712474619}
2025-06-27 21:33:51 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074431989,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.9823182711198428,"volatility":0.282842712474617}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074432128,"reason":"Downward trend detected with -2.60% price decrease (high volatility: 1.2777)"},"trend":"down","priceChange":-2.6027310374994626,"volatility":1.2776649043937252}
2025-06-27 21:33:52 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:52 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:33:52 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7272727272727273,"timestamp":1751074432563,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.45454545454545453,"volatility":0.1414213562373115}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7274795268425842,"timestamp":1751074432626,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.4549590536851683,"volatility":0.1414213562373075}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7276867030965392,"timestamp":1751074432689,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45537340619307837,"volatility":0.1414213562373095}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.727894257064722,"timestamp":1751074432751,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4557885141294439,"volatility":0.1414213562373075}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7281021897810219,"timestamp":1751074432813,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45620437956204385,"volatility":0.14142135623731153}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728310502283105,"timestamp":1751074432875,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45662100456621,"volatility":0.1414213562373115}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7285191956124315,"timestamp":1751074432938,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4570383912248629,"volatility":0.1414213562373075}
2025-06-27 21:33:52 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7287282708142726,"timestamp":1751074432999,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4574565416285453,"volatility":0.1414213562373095}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728937728937729,"timestamp":1751074433062,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4578754578754579,"volatility":0.1414213562373075}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.729147571035747,"timestamp":1751074433125,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.458295142071494,"volatility":0.14142135623731153}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7293577981651376,"timestamp":1751074433187,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45871559633027525,"volatility":0.1414213562373115}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7295684113865932,"timestamp":1751074433248,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4591368227731864,"volatility":0.1414213562373075}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074433263,"reason":"Downward trend detected with -8.74% price decrease (high volatility: 3.7057)"},"trend":"down","priceChange":-8.741698487275148,"volatility":3.705736686768613}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7308402585410896,"timestamp":1751074433621,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46168051708217916,"volatility":0.1414213562373095}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7310536044362292,"timestamp":1751074433682,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4621072088724584,"volatility":0.1414213562373075}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7312673450508789,"timestamp":1751074433745,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46253469010175763,"volatility":0.14142135623731153}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074433808,"reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)"},"trend":"down","priceChange":-21.296296296296298,"volatility":9.100549433962765}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074433870,"reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)"},"trend":"down","priceChange":-20.852641334569043,"volatility":11.023611023616533}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074433932,"reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)"},"trend":"down","priceChange":-20.408163265306122,"volatility":10.903210536351208}
2025-06-27 21:33:53 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074433995,"reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)"},"trend":"down","priceChange":-19.96285979572888,"volatility":8.809086218218095}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434120,"reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.3529411764705883,"volatility":0.565685424949236}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434183,"reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.34192037470726,"volatility":0.56568542494924}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434246,"reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.331002331002331,"volatility":0.565685424949238}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434277,"reason":"Upward trend detected with 15.82% price increase (high volatility: 5.0709)"},"trend":"up","priceChange":15.820984849874971,"volatility":5.070876559478327}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434308,"reason":"Upward trend detected with 1.85% price increase (high volatility: 4.9114)"},"trend":"up","priceChange":1.8475750577367305,"volatility":4.9113913402488025}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434369,"reason":"Upward trend detected with 1.84% price increase (high volatility: 4.7519)"},"trend":"up","priceChange":1.8390804597701083,"volatility":4.7519407311829065}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434431,"reason":"Upward trend detected with 1.83% price increase (high volatility: 4.5925)"},"trend":"up","priceChange":1.8306636155606342,"volatility":4.592528337232045}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434620,"reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2675736961451247,"volatility":0.56568542494924}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434682,"reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2573363431151243,"volatility":0.565685424949236}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434744,"reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.247191011235955,"volatility":0.565685424949236}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434807,"reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2371364653243844,"volatility":0.56568542494924}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434869,"reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2271714922048997,"volatility":0.565685424949238}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434932,"reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2172949002217295,"volatility":0.56568542494924}
2025-06-27 21:33:54 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074434995,"reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.207505518763797,"volatility":0.565685424949236}
2025-06-27 21:33:55 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074435289,"reason":"Upward trend detected with 9.61% price increase (high volatility: 3.1242)"},"trend":"up","priceChange":9.611108188367988,"volatility":3.1241561072668786}
2025-06-27 21:33:55 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:55 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:55 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:33:55 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:33:55 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074598490,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:36:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751074598534,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:36:38 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:39 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:39 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:39 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:39 [ERROR]: API error {"service":"deriv-bot","environment":"demo","error":"Mock connection error","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)"}
2025-06-27 21:36:40 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:40 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:40 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074600693,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:36:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074600802,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:36:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074600911,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074601019,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074601128,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074601161,"reason":"Downward trend detected with -1.88% price decrease (high volatility: 0.9784)"},"trend":"down","priceChange":-1.8754982227552366,"volatility":0.9783617661842965}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7162055335968369,"timestamp":1751074601238,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.0568)"},"trend":"up","priceChange":0.7905138339920921,"volatility":1.0567989168475498}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7155818540433914,"timestamp":1751074601348,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.1355)"},"trend":"up","priceChange":0.7889546351084784,"volatility":1.1354541627776322}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7149606299212644,"timestamp":1751074601457,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.2143)"},"trend":"up","priceChange":0.7874015748031609,"volatility":1.2142851233906948}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7143418467583486,"timestamp":1751074601566,"reason":"Upward trend detected with 0.79% price increase (high volatility: 1.2933)"},"trend":"up","priceChange":0.7858546168958714,"volatility":1.2932596668909346}
2025-06-27 21:36:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074601674,"reason":"Upward trend detected with 3.73% price increase"},"trend":"up","priceChange":3.727587273645895,"volatility":0.282842712474617}
2025-06-27 21:36:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074602169,"reason":"Downward trend detected with -1.99% price decrease (high volatility: 1.0325)"},"trend":"down","priceChange":-1.992518647736017,"volatility":1.0325031995653404}
2025-06-27 21:36:42 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:42 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:42 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:42 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:43 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:43 [INFO]: Subscribed to R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603665,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.5,"volatility":0.4242640687119305}
2025-06-27 21:36:43 [INFO]: [TRADE] Executing trade {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603665,"reason":"Upward trend detected with 1.50% price increase"},"stake":10,"symbol":"R_100"}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603667,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.5,"volatility":0.4242640687119305}
2025-06-27 21:36:43 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:43 [ERROR]: Failed to execute trade {"service":"deriv-bot","environment":"demo","error":"this.api.getContract is not a function","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)"}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603772,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.4955134596211366,"volatility":0.4242640687119285}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603773,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.4955134596211366,"volatility":0.4242640687119285}
2025-06-27 21:36:43 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603881,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4910536779324057,"volatility":0.42426406871192446}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603882,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4910536779324057,"volatility":0.42426406871192446}
2025-06-27 21:36:43 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603993,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4866204162537164,"volatility":0.4242640687119285}
2025-06-27 21:36:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074603995,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4866204162537164,"volatility":0.4242640687119285}
2025-06-27 21:36:43 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074604101,"reason":"Upward trend detected with 1.48% price increase"},"trend":"up","priceChange":1.4822134387351777,"volatility":0.4242640687119305}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074604102,"reason":"Upward trend detected with 1.48% price increase"},"trend":"up","priceChange":1.4822134387351777,"volatility":0.4242640687119305}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074604132,"reason":"Downward trend detected with -1.80% price decrease (high volatility: 1.0753)"},"trend":"down","priceChange":-1.8043478561278945,"volatility":1.0752632018970827}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074604133,"reason":"Downward trend detected with -1.80% price decrease (high volatility: 1.0753)"},"trend":"down","priceChange":-1.8043478561278945,"volatility":1.0752632018970827}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604211,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1910)"},"trend":"up","priceChange":1.178781925343814,"volatility":1.1909687688831594}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604213,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.1910)"},"trend":"up","priceChange":1.178781925343814,"volatility":1.1909687688831594}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604320,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.3074)"},"trend":"up","priceChange":1.175318315377084,"volatility":1.3074487613501986}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604321,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.3074)"},"trend":"up","priceChange":1.175318315377084,"volatility":1.3074487613501986}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604430,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4245)"},"trend":"up","priceChange":1.171874999999989,"volatility":1.4245132216505634}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604432,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4245)"},"trend":"up","priceChange":1.171874999999989,"volatility":1.4245132216505634}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604541,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.5420)"},"trend":"up","priceChange":1.1684518013631966,"volatility":1.5420290443951996}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074604542,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.5420)"},"trend":"up","priceChange":1.1684518013631966,"volatility":1.5420290443951996}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074604652,"reason":"Upward trend detected with 4.55% price increase"},"trend":"up","priceChange":4.546480705433136,"volatility":0.4242640687119305}
2025-06-27 21:36:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074604652,"reason":"Upward trend detected with 4.55% price increase"},"trend":"up","priceChange":4.546480705433136,"volatility":0.4242640687119305}
2025-06-27 21:36:44 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:45 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074605144,"reason":"Downward trend detected with -2.94% price decrease (high volatility: 1.5401)"},"trend":"down","priceChange":-2.93828403456182,"volatility":1.540074951266232}
2025-06-27 21:36:45 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074605146,"reason":"Downward trend detected with -2.94% price decrease (high volatility: 1.5401)"},"trend":"down","priceChange":-2.93828403456182,"volatility":1.540074951266232}
2025-06-27 21:36:45 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:36:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:36:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:45 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:45 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:45 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606109,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606171,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606232,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606295,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606356,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606418,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9900990099009901,"volatility":0.282842712474617}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606480,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9881422924901186,"volatility":0.282842712474619}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606544,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9861932938856016,"volatility":0.28284271247462306}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606605,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.984251968503937,"volatility":0.282842712474619}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751074606668,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.9823182711198428,"volatility":0.282842712474617}
2025-06-27 21:36:46 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074606809,"reason":"Downward trend detected with -1.65% price decrease (high volatility: 0.8952)"},"trend":"down","priceChange":-1.6485363004413083,"volatility":0.8952293924085647}
2025-06-27 21:36:46 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:46 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:46 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:36:46 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7272727272727273,"timestamp":1751074607243,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.45454545454545453,"volatility":0.1414213562373115}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7274795268425842,"timestamp":1751074607304,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.4549590536851683,"volatility":0.1414213562373075}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7276867030965392,"timestamp":1751074607366,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45537340619307837,"volatility":0.1414213562373095}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.727894257064722,"timestamp":1751074607429,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4557885141294439,"volatility":0.1414213562373075}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7281021897810219,"timestamp":1751074607491,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45620437956204385,"volatility":0.14142135623731153}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728310502283105,"timestamp":1751074607553,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45662100456621,"volatility":0.1414213562373115}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7285191956124315,"timestamp":1751074607616,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4570383912248629,"volatility":0.1414213562373075}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7287282708142726,"timestamp":1751074607679,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4574565416285453,"volatility":0.1414213562373095}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728937728937729,"timestamp":1751074607741,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4578754578754579,"volatility":0.1414213562373075}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.729147571035747,"timestamp":1751074607802,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.458295142071494,"volatility":0.14142135623731153}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7293577981651376,"timestamp":1751074607864,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45871559633027525,"volatility":0.1414213562373115}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7295684113865932,"timestamp":1751074607926,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4591368227731864,"volatility":0.1414213562373075}
2025-06-27 21:36:47 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074607941,"reason":"Downward trend detected with -7.49% price decrease (high volatility: 3.1591)"},"trend":"down","priceChange":-7.485144791331229,"volatility":3.159118130033135}
2025-06-27 21:36:48 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7070591735290264,"timestamp":1751074608236,"reason":"Bullish MA crossover: Short MA (108.1000) > Long MA (108.0237)"},"shortMA":108.1,"longMA":108.02374416446878,"prevShortMA":106.65123249340631,"prevLongMA":108.1170774978021}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7308402585410896,"timestamp":1751074608299,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46168051708217916,"volatility":0.1414213562373095}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7310536044362292,"timestamp":1751074608364,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4621072088724584,"volatility":0.1414213562373075}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7312673450508789,"timestamp":1751074608423,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46253469010175763,"volatility":0.14142135623731153}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074608485,"reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)"},"trend":"down","priceChange":-21.296296296296298,"volatility":9.100549433962765}
2025-06-27 21:36:48 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751074608487,"reason":"Bearish MA crossover: Short MA (103.2000) < Long MA (106.1504)"},"shortMA":103.2,"longMA":106.15041083113543,"prevShortMA":107.8,"prevLongMA":107.74374416446878}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074608547,"reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)"},"trend":"down","priceChange":-20.852641334569043,"volatility":11.023611023616533}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074608609,"reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)"},"trend":"down","priceChange":-20.408163265306122,"volatility":10.903210536351208}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751074608670,"reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)"},"trend":"down","priceChange":-19.96285979572888,"volatility":8.809086218218095}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074608795,"reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.3529411764705883,"volatility":0.565685424949236}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074608857,"reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.34192037470726,"volatility":0.56568542494924}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074608919,"reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.331002331002331,"volatility":0.565685424949238}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074608951,"reason":"Upward trend detected with 17.86% price increase (high volatility: 5.7720)"},"trend":"up","priceChange":17.860128147100944,"volatility":5.772048762224236}
2025-06-27 21:36:48 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074608981,"reason":"Upward trend detected with 1.85% price increase (high volatility: 5.6124)"},"trend":"up","priceChange":1.8475750577367305,"volatility":5.6124443707047815}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609045,"reason":"Upward trend detected with 1.84% price increase (high volatility: 5.4529)"},"trend":"up","priceChange":1.8390804597701083,"volatility":5.452863166724182}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609106,"reason":"Upward trend detected with 1.83% price increase (high volatility: 5.2933)"},"trend":"up","priceChange":1.8306636155606342,"volatility":5.293307247437914}
2025-06-27 21:36:49 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7520126508690403,"timestamp":1751074609168,"reason":"Bullish MA crossover: Short MA (91.3591) > Long MA (90.8864)"},"shortMA":91.3590860925602,"longMA":90.8863620308534,"prevShortMA":91.03908609256021,"prevLongMA":92.11302869752006}
2025-06-27 21:36:49 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7772667293750568,"timestamp":1751074609229,"reason":"Bearish MA crossover: Short MA (89.0000) < Long MA (89.6930)"},"shortMA":89.00000000000001,"longMA":89.69302869752006,"prevShortMA":91.3590860925602,"prevLongMA":90.8863620308534}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609293,"reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2675736961451247,"volatility":0.56568542494924}
2025-06-27 21:36:49 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7979263123869865,"timestamp":1751074609295,"reason":"Bullish MA crossover: Short MA (89.4000) > Long MA (88.5330)"},"shortMA":89.4,"longMA":88.53302869752007,"prevShortMA":89.00000000000001,"prevLongMA":89.69302869752006}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609355,"reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2573363431151243,"volatility":0.565685424949236}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609417,"reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.247191011235955,"volatility":0.565685424949236}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609480,"reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2371364653243844,"volatility":0.56568542494924}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609544,"reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2271714922048997,"volatility":0.565685424949238}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609605,"reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2172949002217295,"volatility":0.56568542494924}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609666,"reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.207505518763797,"volatility":0.565685424949236}
2025-06-27 21:36:49 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751074609963,"reason":"Upward trend detected with 11.86% price increase (high volatility: 3.9386)"},"trend":"up","priceChange":11.863214122989612,"volatility":3.9385745060789294}
2025-06-27 21:36:50 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:50 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:36:50 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:36:50 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075074243,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.2872281323269024}
2025-06-27 21:44:34 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751075074270,"reason":"Downward trend detected with -1.00% price decrease"},"trend":"down","priceChange":-1.0040160642570282,"volatility":0.2872281323269024}
2025-06-27 21:44:34 [INFO]: Strategy reset {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:34 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:34 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:34 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [ERROR]: API error {"service":"deriv-bot","environment":"demo","error":"Mock connection error","stack":"Error: Mock connection error\n    at MockDerivAPIService.simulateConnectionError (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\test-utils\\MockDerivAPIService.ts:236:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\__tests__\\DerivBot.integration.test.ts:91:15)"}
2025-06-27 21:44:34 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:34 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:34 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:34 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075075417,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075075527,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075075636,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075075746,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075075855,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7735945623830626,"timestamp":1751075075871,"reason":"Downward trend detected with -0.55% price decrease"},"trend":"down","priceChange":-0.5471891247661251,"volatility":0.4661493691314268}
2025-06-27 21:44:35 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7162055335968369,"timestamp":1751075075963,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.5395)"},"trend":"up","priceChange":0.7905138339920921,"volatility":0.5395049020782942}
2025-06-27 21:44:36 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7155818540433914,"timestamp":1751075076073,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6145)"},"trend":"up","priceChange":0.7889546351084784,"volatility":0.6145208250266165}
2025-06-27 21:44:36 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7149606299212644,"timestamp":1751075076181,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.6907)"},"trend":"up","priceChange":0.7874015748031609,"volatility":0.6906563178718321}
2025-06-27 21:44:36 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7143418467583486,"timestamp":1751075076290,"reason":"Upward trend detected with 0.79% price increase (high volatility: 0.7676)"},"trend":"up","priceChange":0.7858546168958714,"volatility":0.7675783050876032}
2025-06-27 21:44:36 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075076399,"reason":"Upward trend detected with 2.34% price increase"},"trend":"up","priceChange":2.342183516069904,"volatility":0.282842712474617}
2025-06-27 21:44:36 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075076883,"reason":"Downward trend detected with -1.59% price decrease (high volatility: 0.8715)"},"trend":"down","priceChange":-1.5887750261101123,"volatility":0.8714800776610401}
2025-06-27 21:44:37 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:37 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:37 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:37 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:37 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:37 [INFO]: Subscribed to R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078395,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.5,"volatility":0.4242640687119305}
2025-06-27 21:44:38 [INFO]: [TRADE] Executing trade {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078395,"reason":"Upward trend detected with 1.50% price increase"},"stake":10,"symbol":"R_100"}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078397,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.5,"volatility":0.4242640687119305}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [ERROR]: Failed to execute trade {"service":"deriv-bot","environment":"demo","error":"this.api.getContract is not a function","stack":"TypeError: this.api.getContract is not a function\n    at DerivBot.executeTrade (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:248:24)\n    at DerivBot.handleTick (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\bot\\DerivBot.ts:175:11)"}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078504,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.4955134596211366,"volatility":0.4242640687119285}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078505,"reason":"Upward trend detected with 1.50% price increase"},"trend":"up","priceChange":1.4955134596211366,"volatility":0.4242640687119285}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078613,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4910536779324057,"volatility":0.42426406871192446}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078614,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4910536779324057,"volatility":0.42426406871192446}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078723,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4866204162537164,"volatility":0.4242640687119285}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078724,"reason":"Upward trend detected with 1.49% price increase"},"trend":"up","priceChange":1.4866204162537164,"volatility":0.4242640687119285}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078830,"reason":"Upward trend detected with 1.48% price increase"},"trend":"up","priceChange":1.4822134387351777,"volatility":0.4242640687119305}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075078831,"reason":"Upward trend detected with 1.48% price increase"},"trend":"up","priceChange":1.4822134387351777,"volatility":0.4242640687119305}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075078862,"reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.9833)"},"trend":"down","priceChange":-1.5675876828518387,"volatility":0.9833214102728257}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075078863,"reason":"Downward trend detected with -1.57% price decrease (high volatility: 0.9833)"},"trend":"down","priceChange":-1.5675876828518387,"volatility":0.9833214102728257}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075078941,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0982)"},"trend":"up","priceChange":1.178781925343814,"volatility":1.0982106991456702}
2025-06-27 21:44:38 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075078943,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.0982)"},"trend":"up","priceChange":1.178781925343814,"volatility":1.0982106991456702}
2025-06-27 21:44:38 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079051,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.2141)"},"trend":"up","priceChange":1.175318315377084,"volatility":1.2140891579843316}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079052,"reason":"Upward trend detected with 1.18% price increase (high volatility: 1.2141)"},"trend":"up","priceChange":1.175318315377084,"volatility":1.2140891579843316}
2025-06-27 21:44:39 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079161,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3307)"},"trend":"up","priceChange":1.171874999999989,"volatility":1.3306983983428338}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079162,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.3307)"},"trend":"up","priceChange":1.171874999999989,"volatility":1.3306983983428338}
2025-06-27 21:44:39 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079270,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4479)"},"trend":"up","priceChange":1.1684518013631966,"volatility":1.4478618619085422}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075079271,"reason":"Upward trend detected with 1.17% price increase (high volatility: 1.4479)"},"trend":"up","priceChange":1.1684518013631966,"volatility":1.4478618619085422}
2025-06-27 21:44:39 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075079379,"reason":"Upward trend detected with 4.30% price increase"},"trend":"up","priceChange":4.295014320483959,"volatility":0.4242640687119305}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075079380,"reason":"Upward trend detected with 4.30% price increase"},"trend":"up","priceChange":4.295014320483959,"volatility":0.4242640687119305}
2025-06-27 21:44:39 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075079864,"reason":"Downward trend detected with -3.54% price decrease (high volatility: 1.7828)"},"trend":"down","priceChange":-3.5373391661289064,"volatility":1.7828061019420411}
2025-06-27 21:44:39 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075079865,"reason":"Downward trend detected with -3.54% price decrease (high volatility: 1.7828)"},"trend":"down","priceChange":-3.5373391661289064,"volatility":1.7828061019420411}
2025-06-27 21:44:39 [WARN]: [RISK] Daily loss limit reached {"service":"deriv-bot","environment":"demo","dailyProfit":-2,"maxDailyLoss":1}
2025-06-27 21:44:39 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":-2,"totalTrades":0}
2025-06-27 21:44:39 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:39 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:39 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:40 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:40 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:40 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:40 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075080813,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":1,"volatility":0.282842712474617}
2025-06-27 21:44:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075080876,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.998003992015968,"volatility":0.282842712474619}
2025-06-27 21:44:40 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075080939,"reason":"Upward trend detected with 1.00% price increase"},"trend":"up","priceChange":0.9960159362549801,"volatility":0.28284271247462306}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081001,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9940357852882704,"volatility":0.282842712474619}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081064,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.992063492063492,"volatility":0.282842712474617}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081127,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9900990099009901,"volatility":0.282842712474617}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081189,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9881422924901186,"volatility":0.282842712474619}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081252,"reason":"Upward trend detected with 0.99% price increase"},"trend":"up","priceChange":0.9861932938856016,"volatility":0.28284271247462306}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081316,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.984251968503937,"volatility":0.282842712474619}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.9,"timestamp":1751075081379,"reason":"Upward trend detected with 0.98% price increase"},"trend":"up","priceChange":0.9823182711198428,"volatility":0.282842712474617}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075081520,"reason":"Downward trend detected with -1.26% price decrease (high volatility: 0.7411)"},"trend":"down","priceChange":-1.2588536999174391,"volatility":0.7411089854836259}
2025-06-27 21:44:41 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:41 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Trading is disabled. Bot will only monitor market data. {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: Starting monitoring mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:44:41 [INFO]: Monitoring R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:41 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7272727272727273,"timestamp":1751075081958,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.45454545454545453,"volatility":0.1414213562373115}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7274795268425842,"timestamp":1751075082020,"reason":"Downward trend detected with -0.45% price decrease"},"trend":"down","priceChange":-0.4549590536851683,"volatility":0.1414213562373075}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7276867030965392,"timestamp":1751075082082,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45537340619307837,"volatility":0.1414213562373095}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.727894257064722,"timestamp":1751075082145,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4557885141294439,"volatility":0.1414213562373075}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7281021897810219,"timestamp":1751075082208,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45620437956204385,"volatility":0.14142135623731153}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728310502283105,"timestamp":1751075082269,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45662100456621,"volatility":0.1414213562373115}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7285191956124315,"timestamp":1751075082331,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4570383912248629,"volatility":0.1414213562373075}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7287282708142726,"timestamp":1751075082393,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4574565416285453,"volatility":0.1414213562373095}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.728937728937729,"timestamp":1751075082457,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4578754578754579,"volatility":0.1414213562373075}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.729147571035747,"timestamp":1751075082517,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.458295142071494,"volatility":0.14142135623731153}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7293577981651376,"timestamp":1751075082580,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.45871559633027525,"volatility":0.1414213562373115}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7295684113865932,"timestamp":1751075082642,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4591368227731864,"volatility":0.1414213562373075}
2025-06-27 21:44:42 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075082658,"reason":"Downward trend detected with -8.47% price decrease (high volatility: 3.5876)"},"trend":"down","priceChange":-8.470085732826137,"volatility":3.587575280720178}
2025-06-27 21:44:42 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7136816798616307,"timestamp":1751075082954,"reason":"Bullish MA crossover: Short MA (108.1000) > Long MA (107.9523)"},"shortMA":108.1,"longMA":107.95230311484569,"prevShortMA":106.43690934453703,"prevLongMA":108.04563644817901}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7308402585410896,"timestamp":1751075083016,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46168051708217916,"volatility":0.1414213562373095}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7310536044362292,"timestamp":1751075083078,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.4621072088724584,"volatility":0.1414213562373075}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7312673450508789,"timestamp":1751075083139,"reason":"Downward trend detected with -0.46% price decrease"},"trend":"down","priceChange":-0.46253469010175763,"volatility":0.14142135623731153}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075083201,"reason":"Downward trend detected with -21.30% price decrease (high volatility: 9.1005)"},"trend":"down","priceChange":-21.296296296296298,"volatility":9.100549433962765}
2025-06-27 21:44:43 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.9,"timestamp":1751075083202,"reason":"Bearish MA crossover: Short MA (103.2000) < Long MA (106.0790)"},"shortMA":103.2,"longMA":106.07896978151236,"prevShortMA":107.8,"prevLongMA":107.67230311484566}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075083263,"reason":"Downward trend detected with -20.85% price decrease (high volatility: 11.0236)"},"trend":"down","priceChange":-20.852641334569043,"volatility":11.023611023616533}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075083324,"reason":"Downward trend detected with -20.41% price decrease (high volatility: 10.9032)"},"trend":"down","priceChange":-20.408163265306122,"volatility":10.903210536351208}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7200000000000001,"timestamp":1751075083387,"reason":"Downward trend detected with -19.96% price decrease (high volatility: 8.8091)"},"trend":"down","priceChange":-19.96285979572888,"volatility":8.809086218218095}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083513,"reason":"Upward trend detected with 2.35% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.3529411764705883,"volatility":0.565685424949236}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083574,"reason":"Upward trend detected with 2.34% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.34192037470726,"volatility":0.56568542494924}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083637,"reason":"Upward trend detected with 2.33% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.331002331002331,"volatility":0.565685424949238}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083669,"reason":"Upward trend detected with 15.26% price increase (high volatility: 4.8773)"},"trend":"up","priceChange":15.257714189451042,"volatility":4.877290078093286}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083699,"reason":"Upward trend detected with 1.85% price increase (high volatility: 4.7178)"},"trend":"up","priceChange":1.8475750577367305,"volatility":4.717847321931893}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083761,"reason":"Upward trend detected with 1.84% price increase (high volatility: 4.5584)"},"trend":"up","priceChange":1.8390804597701083,"volatility":4.558443616000165}
2025-06-27 21:44:43 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075083825,"reason":"Upward trend detected with 1.83% price increase (high volatility: 4.3991)"},"trend":"up","priceChange":1.8306636155606342,"volatility":4.399083205333288}
2025-06-27 21:44:43 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7191344561141083,"timestamp":1751075083888,"reason":"Bullish MA crossover: Short MA (90.9104) > Long MA (90.7368)"},"shortMA":90.91042992626134,"longMA":90.73680997542046,"prevShortMA":90.59042992626136,"prevLongMA":91.9634766420871}
2025-06-27 21:44:43 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"PUT","confidence":0.7606941636027196,"timestamp":1751075083950,"reason":"Bearish MA crossover: Short MA (89.0000) < Long MA (89.5435)"},"shortMA":89.00000000000001,"longMA":89.54347664208713,"prevShortMA":90.91042992626134,"prevLongMA":90.73680997542046}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084013,"reason":"Upward trend detected with 2.27% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2675736961451247,"volatility":0.56568542494924}
2025-06-27 21:44:44 [INFO]: [STRATEGY] MA Crossover signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.8150128277969118,"timestamp":1751075084014,"reason":"Bullish MA crossover: Short MA (89.4000) > Long MA (88.3835)"},"shortMA":89.4,"longMA":88.38347664208713,"prevShortMA":89.00000000000001,"prevLongMA":89.54347664208713}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084077,"reason":"Upward trend detected with 2.26% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2573363431151243,"volatility":0.565685424949236}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084139,"reason":"Upward trend detected with 2.25% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.247191011235955,"volatility":0.565685424949236}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084201,"reason":"Upward trend detected with 2.24% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2371364653243844,"volatility":0.56568542494924}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084264,"reason":"Upward trend detected with 2.23% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2271714922048997,"volatility":0.565685424949238}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084327,"reason":"Upward trend detected with 2.22% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.2172949002217295,"volatility":0.56568542494924}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084389,"reason":"Upward trend detected with 2.21% price increase (high volatility: 0.5657)"},"trend":"up","priceChange":2.207505518763797,"volatility":0.565685424949236}
2025-06-27 21:44:44 [INFO]: [STRATEGY] Signal generated {"service":"deriv-bot","environment":"demo","signal":{"direction":"CALL","confidence":0.7200000000000001,"timestamp":1751075084682,"reason":"Upward trend detected with 8.25% price increase (high volatility: 2.6336)"},"trend":"up","priceChange":8.250123809834129,"volatility":2.6335989861187543}
2025-06-27 21:44:44 [INFO]: Stopping Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:44 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:44 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:44 [INFO]: Bot Statistics {"service":"deriv-bot","environment":"demo","stats":{"totalTrades":0,"winRate":0,"totalProfit":0,"dailyProfit":0,"consecutiveLosses":0,"isActive":false,"lastTradeTime":0},"dailyProfit":0,"totalTrades":0}
2025-06-27 21:44:44 [INFO]: Bot stopped {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:44 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:44:44 [WARN]: API disconnected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: 🚀 Starting Deriv Bot Demo with your API key... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: 📊 Configuration loaded: {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - Environment: demo {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - Trading Enabled: true {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - Symbol: R_100 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - Stake: 1 USD {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - Strategy: simple_trend {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: 🎯 Starting bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:07 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:46:37 [ERROR]: Failed to get balance {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:46:37 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 21:47:07 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:49:59 [INFO]: 🔍 Testing Deriv API Connection... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: 📊 Using configuration: {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: - App ID: 1089 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: - API URL: wss://ws.binaryws.com/websockets/v3 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: 🔌 Connecting to Deriv API... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: 📊 Testing basic API calls... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: 📋 Getting active symbols... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: ✅ Active symbols received: 85 symbols {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:49:59 [INFO]: 💰 Getting account balance... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:50:29 [ERROR]: ❌ Get balance failed: {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:50:29 [INFO]: 📈 Testing tick subscription... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:50:59 [ERROR]: ❌ Tick subscription failed: {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:50:59 [INFO]: 🧹 Cleaning up... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:50:59 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:51:00 [INFO]: ✅ Connection test completed {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: 🔍 Testing Deriv API Connection... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: 📊 Using configuration: {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: - App ID: 1089 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: - API URL: wss://ws.binaryws.com/websockets/v3 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:33 [INFO]: 🔌 Connecting to Deriv API... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:34 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:52:34 [INFO]: 🔐 Authorizing with API token... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:53:04 [ERROR]: ❌ Authorization failed: {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:54:48 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:54:49 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:01 [INFO]: 🔍 Simple WebSocket Test... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:01 [INFO]: 📡 Connecting to: wss://ws.binaryws.com/websockets/v3?app_id=1089&l=en {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:01 [INFO]: ✅ WebSocket connected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:01 [INFO]: 📋 Testing active symbols... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:03 [INFO]: 🔐 Testing authorization... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:29 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:30 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:31 [WARN]: ⏰ Test timeout, closing... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:56:31 [INFO]: 🔚 WebSocket closed {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: 🔍 Simple WebSocket Test... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: 📡 Connecting to: wss://ws.binaryws.com/websockets/v3?app_id=1089&l=en {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: ✅ WebSocket connected {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: 📋 Testing active symbols... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: 📤 Sending: {"active_symbols":"brief","product_type":"basic","req_id":1} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: 📨 Received message: {
  "active_symbols": [
    {
      "allow_forward_starting": 0,
      "display_name": "AUD Basket",
      "display_order": 30,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "baskets",
      "subgroup_display_name": "Baskets",
      "submarket": "forex_basket",
      "submarket_display_name": "Forex Basket",
      "symbol": "WLDAUD",
      "symbol_type": "forex_basket"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "AUD/CAD",
      "display_order": 10,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxAUDCAD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "AUD/CHF",
      "display_order": 16,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxAUDCHF",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "AUD/JPY",
      "display_order": 1,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxAUDJPY",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "AUD/NZD",
      "display_order": 18,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxAUDNZD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "AUD/USD",
      "display_order": 2,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxAUDUSD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Australia 200",
      "display_order": 4,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "asia_oceania_OTC",
      "submarket_display_name": "Asian indices",
      "symbol": "OTC_AS51",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "BTC/USD",
      "display_order": 0,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "cryptocurrency",
      "market_display_name": "Cryptocurrencies",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "non_stable_coin",
      "submarket_display_name": "Cryptocurrencies",
      "symbol": "cryBTCUSD",
      "symbol_type": "cryptocurrency"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Bear Market Index",
      "display_order": 10,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.0001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_daily",
      "submarket_display_name": "Daily Reset Indices",
      "symbol": "RDBEAR",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Boom 300 Index",
      "display_order": 26,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "BOOM300N",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Boom 500 Index",
      "display_order": 18,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "BOOM500",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Boom 600 Index",
      "display_order": 33,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "BOOM600",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Boom 900 Index",
      "display_order": 36,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "BOOM900",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Boom 1000 Index",
      "display_order": 21,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "BOOM1000",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Bull Market Index",
      "display_order": 12,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.0001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_daily",
      "submarket_display_name": "Daily Reset Indices",
      "symbol": "RDBULL",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Crash 300 Index",
      "display_order": 28,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "CRASH300N",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Crash 500 Index",
      "display_order": 19,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "CRASH500",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Crash 600 Index",
      "display_order": 31,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "CRASH600",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Crash 900 Index",
      "display_order": 35,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "CRASH900",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Crash 1000 Index",
      "display_order": 22,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "crash_index",
      "submarket_display_name": "Crash/Boom Indices",
      "symbol": "CRASH1000",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 0,
      "display_name": "ETH/USD",
      "display_order": 1,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "cryptocurrency",
      "market_display_name": "Cryptocurrencies",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "non_stable_coin",
      "submarket_display_name": "Cryptocurrencies",
      "symbol": "cryETHUSD",
      "symbol_type": "cryptocurrency"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "EUR Basket",
      "display_order": 37,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "baskets",
      "subgroup_display_name": "Baskets",
      "submarket": "forex_basket",
      "submarket_display_name": "Forex Basket",
      "symbol": "WLDEUR",
      "symbol_type": "forex_basket"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/AUD",
      "display_order": 6,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURAUD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/CAD",
      "display_order": 14,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURCAD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/CHF",
      "display_order": 13,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURCHF",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/GBP",
      "display_order": 9,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURGBP",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/JPY",
      "display_order": 8,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURJPY",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/NZD",
      "display_order": 19,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxEURNZD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "EUR/USD",
      "display_order": 0,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxEURUSD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Euro 50",
      "display_order": 8,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_SX5E",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "France 40",
      "display_order": 9,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_FCHI",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "GBP Basket",
      "display_order": 38,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "baskets",
      "subgroup_display_name": "Baskets",
      "submarket": "forex_basket",
      "submarket_display_name": "Forex Basket",
      "symbol": "WLDGBP",
      "symbol_type": "forex_basket"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/AUD",
      "display_order": 11,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxGBPAUD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/CAD",
      "display_order": 17,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxGBPCAD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/CHF",
      "display_order": 20,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxGBPCHF",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/JPY",
      "display_order": 5,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxGBPJPY",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/NOK",
      "display_order": 27,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxGBPNOK",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/NZD",
      "display_order": 21,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxGBPNZD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "GBP/USD",
      "display_order": 4,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxGBPUSD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Germany 40",
      "display_order": 7,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_GDAXI",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Gold Basket",
      "display_order": 32,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "baskets",
      "subgroup_display_name": "Baskets",
      "submarket": "commodity_basket",
      "submarket_display_name": "Commodities Basket",
      "symbol": "WLDXAU",
      "symbol_type": "commodity_basket"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Gold/USD",
      "display_order": 0,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "commodities",
      "market_display_name": "Commodities",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "metals",
      "submarket_display_name": "Metals",
      "symbol": "frxXAUUSD",
      "symbol_type": "commodities"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Hong Kong 50",
      "display_order": 10,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "asia_oceania_OTC",
      "submarket_display_name": "Asian indices",
      "symbol": "OTC_HSI",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Japan 225",
      "display_order": 6,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "asia_oceania_OTC",
      "submarket_display_name": "Asian indices",
      "symbol": "OTC_N225",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Jump 10 Index",
      "display_order": 11,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "jump_index",
      "submarket_display_name": "Jump Indices",
      "symbol": "JD10",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Jump 25 Index",
      "display_order": 16,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "jump_index",
      "submarket_display_name": "Jump Indices",
      "symbol": "JD25",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Jump 50 Index",
      "display_order": 17,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "jump_index",
      "submarket_display_name": "Jump Indices",
      "symbol": "JD50",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Jump 75 Index",
      "display_order": 14,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "jump_index",
      "submarket_display_name": "Jump Indices",
      "symbol": "JD75",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Jump 100 Index",
      "display_order": 13,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "jump_index",
      "submarket_display_name": "Jump Indices",
      "symbol": "JD100",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "NZD/JPY",
      "display_order": 23,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxNZDJPY",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "NZD/USD",
      "display_order": 15,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxNZDUSD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Netherlands 25",
      "display_order": 11,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_AEX",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Palladium/USD",
      "display_order": 2,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "commodities",
      "market_display_name": "Commodities",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "metals",
      "submarket_display_name": "Metals",
      "symbol": "frxXPDUSD",
      "symbol_type": "commodities"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Platinum/USD",
      "display_order": 3,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "commodities",
      "market_display_name": "Commodities",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "metals",
      "submarket_display_name": "Metals",
      "symbol": "frxXPTUSD",
      "symbol_type": "commodities"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Silver/USD",
      "display_order": 1,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "commodities",
      "market_display_name": "Commodities",
      "pip": 0.0001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "metals",
      "submarket_display_name": "Metals",
      "symbol": "frxXAGUSD",
      "symbol_type": "commodities"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Step Index 100",
      "display_order": 15,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.1,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "step_index",
      "submarket_display_name": "Step Indices",
      "symbol": "stpRNG",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Step Index 200",
      "display_order": 23,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.1,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "step_index",
      "submarket_display_name": "Step Indices",
      "symbol": "stpRNG2",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Step Index 300",
      "display_order": 24,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.1,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "step_index",
      "submarket_display_name": "Step Indices",
      "symbol": "stpRNG3",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Step Index 400",
      "display_order": 25,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.1,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "step_index",
      "submarket_display_name": "Step Indices",
      "symbol": "stpRNG4",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Step Index 500",
      "display_order": 20,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.1,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "step_index",
      "submarket_display_name": "Step Indices",
      "symbol": "stpRNG5",
      "symbol_type": ""
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Swiss 20",
      "display_order": 5,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_SSMI",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "UK 100",
      "display_order": 3,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "europe_OTC",
      "submarket_display_name": "European indices",
      "symbol": "OTC_FTSE",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "US 500",
      "display_order": 0,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "americas_OTC",
      "submarket_display_name": "American indices",
      "symbol": "OTC_SPC",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "US Tech 100",
      "display_order": 1,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "americas_OTC",
      "submarket_display_name": "American indices",
      "symbol": "OTC_NDX",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "USD Basket",
      "display_order": 34,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "baskets",
      "subgroup_display_name": "Baskets",
      "submarket": "forex_basket",
      "submarket_display_name": "Forex Basket",
      "symbol": "WLDUSD",
      "symbol_type": "forex_basket"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/CAD",
      "display_order": 7,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxUSDCAD",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/CHF",
      "display_order": 12,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxUSDCHF",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/JPY",
      "display_order": 3,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "major_pairs",
      "submarket_display_name": "Major Pairs",
      "symbol": "frxUSDJPY",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/MXN",
      "display_order": 22,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.0001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxUSDMXN",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/NOK",
      "display_order": 26,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxUSDNOK",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/PLN",
      "display_order": 24,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.0001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxUSDPLN",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "USD/SEK",
      "display_order": 25,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "forex",
      "market_display_name": "Forex",
      "pip": 0.00001,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "minor_pairs",
      "submarket_display_name": "Minor Pairs",
      "symbol": "frxUSDSEK",
      "symbol_type": "forex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 10 (1s) Index",
      "display_order": 2,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ10V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 10 Index",
      "display_order": 3,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "R_10",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 25 (1s) Index",
      "display_order": 5,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ25V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 25 Index",
      "display_order": 7,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "R_25",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 50 (1s) Index",
      "display_order": 9,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ50V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 50 Index",
      "display_order": 4,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.0001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "R_50",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 75 (1s) Index",
      "display_order": 6,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ75V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 75 Index",
      "display_order": 8,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.0001,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "R_75",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 100 (1s) Index",
      "display_order": 1,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ100V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Volatility 100 Index",
      "display_order": 0,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "R_100",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Volatility 150 (1s) Index",
      "display_order": 27,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ150V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 0,
      "display_name": "Volatility 250 (1s) Index",
      "display_order": 29,
      "exchange_is_open": 1,
      "is_trading_suspended": 0,
      "market": "synthetic_index",
      "market_display_name": "Derived",
      "pip": 0.01,
      "subgroup": "synthetics",
      "subgroup_display_name": "Synthetics",
      "submarket": "random_index",
      "submarket_display_name": "Continuous Indices",
      "symbol": "1HZ250V",
      "symbol_type": "stockindex"
    },
    {
      "allow_forward_starting": 1,
      "display_name": "Wall Street 30",
      "display_order": 2,
      "exchange_is_open": 0,
      "is_trading_suspended": 0,
      "market": "indices",
      "market_display_name": "Stock Indices",
      "pip": 0.01,
      "subgroup": "none",
      "subgroup_display_name": "None",
      "submarket": "americas_OTC",
      "submarket_display_name": "American indices",
      "symbol": "OTC_DJI",
      "symbol_type": "stockindex"
    }
  ],
  "echo_req": {
    "active_symbols": "brief",
    "product_type": "basic",
    "req_id": 1
  },
  "msg_type": "active_symbols",
  "req_id": 1
} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:13 [INFO]: ✅ Active symbols: 85 symbols received {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:15 [INFO]: 🔐 Testing authorization... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:15 [INFO]: 📤 Sending: {"authorize":"hEMSCZEtzrg5BzA","req_id":2} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:15 [INFO]: 📨 Received message: {
  "authorize": {
    "account_list": [
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "CR",
        "created_at": **********,
        "currency": "USD",
        "currency_type": "fiat",
        "is_disabled": 0,
        "is_virtual": 0,
        "landing_company_name": "svg",
        "linked_to": [],
        "loginid": "CR2481365"
      },
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "CR",
        "created_at": **********,
        "currency": "eUSDT",
        "currency_type": "crypto",
        "is_disabled": 0,
        "is_virtual": 0,
        "landing_company_name": "svg",
        "linked_to": [],
        "loginid": "CR5869675"
      },
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "CR",
        "created_at": **********,
        "currency": "tUSDT",
        "currency_type": "crypto",
        "is_disabled": 0,
        "is_virtual": 0,
        "landing_company_name": "svg",
        "linked_to": [],
        "loginid": "CR6683184"
      },
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "CR",
        "created_at": **********,
        "currency": "USDC",
        "currency_type": "crypto",
        "is_disabled": 0,
        "is_virtual": 0,
        "landing_company_name": "svg",
        "linked_to": [],
        "loginid": "CR6748108"
      },
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "CR",
        "created_at": **********,
        "currency": "ETH",
        "currency_type": "crypto",
        "is_disabled": 0,
        "is_virtual": 0,
        "landing_company_name": "svg",
        "linked_to": [],
        "loginid": "CR6766403"
      },
      {
        "account_category": "trading",
        "account_type": "binary",
        "broker": "VRTC",
        "created_at": **********,
        "currency": "USD",
        "currency_type": "fiat",
        "is_disabled": 0,
        "is_virtual": 1,
        "landing_company_name": "virtual",
        "linked_to": [],
        "loginid": "VRTC3871036"
      }
    ],
    "balance": 24856.64,
    "country": "ke",
    "currency": "USD",
    "email": "<EMAIL>",
    "fullname": " Samuel Mweni",
    "is_virtual": 1,
    "landing_company_fullname": "Deriv Limited",
    "landing_company_name": "virtual",
    "linked_to": [],
    "local_currencies": {
      "KES": {
        "fractional_digits": 2
      }
    },
    "loginid": "VRTC3871036",
    "preferred_language": "EN",
    "scopes": [
      "payments",
      "read",
      "trade",
      "trading_information"
    ],
    "upgradeable_landing_companies": [
      "svg"
    ],
    "user_id": 7983887
  },
  "echo_req": {
    "authorize": "<not shown>",
    "req_id": 2
  },
  "msg_type": "authorize",
  "req_id": 2
} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:15 [INFO]: ✅ Authorization successful! {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:15 [INFO]: Account info: {
  "account_list": [
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "CR",
      "created_at": **********,
      "currency": "USD",
      "currency_type": "fiat",
      "is_disabled": 0,
      "is_virtual": 0,
      "landing_company_name": "svg",
      "linked_to": [],
      "loginid": "CR2481365"
    },
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "CR",
      "created_at": **********,
      "currency": "eUSDT",
      "currency_type": "crypto",
      "is_disabled": 0,
      "is_virtual": 0,
      "landing_company_name": "svg",
      "linked_to": [],
      "loginid": "CR5869675"
    },
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "CR",
      "created_at": **********,
      "currency": "tUSDT",
      "currency_type": "crypto",
      "is_disabled": 0,
      "is_virtual": 0,
      "landing_company_name": "svg",
      "linked_to": [],
      "loginid": "CR6683184"
    },
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "CR",
      "created_at": **********,
      "currency": "USDC",
      "currency_type": "crypto",
      "is_disabled": 0,
      "is_virtual": 0,
      "landing_company_name": "svg",
      "linked_to": [],
      "loginid": "CR6748108"
    },
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "CR",
      "created_at": **********,
      "currency": "ETH",
      "currency_type": "crypto",
      "is_disabled": 0,
      "is_virtual": 0,
      "landing_company_name": "svg",
      "linked_to": [],
      "loginid": "CR6766403"
    },
    {
      "account_category": "trading",
      "account_type": "binary",
      "broker": "VRTC",
      "created_at": **********,
      "currency": "USD",
      "currency_type": "fiat",
      "is_disabled": 0,
      "is_virtual": 1,
      "landing_company_name": "virtual",
      "linked_to": [],
      "loginid": "VRTC3871036"
    }
  ],
  "balance": 24856.64,
  "country": "ke",
  "currency": "USD",
  "email": "<EMAIL>",
  "fullname": " Samuel Mweni",
  "is_virtual": 1,
  "landing_company_fullname": "Deriv Limited",
  "landing_company_name": "virtual",
  "linked_to": [],
  "local_currencies": {
    "KES": {
      "fractional_digits": 2
    }
  },
  "loginid": "VRTC3871036",
  "preferred_language": "EN",
  "scopes": [
    "payments",
    "read",
    "trade",
    "trading_information"
  ],
  "upgradeable_landing_companies": [
    "svg"
  ],
  "user_id": 7983887
} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:16 [INFO]: 💰 Testing balance... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:16 [INFO]: 📨 Received message: {
  "balance": {
    "balance": 24856.64,
    "currency": "USD",
    "loginid": "VRTC3871036"
  },
  "echo_req": {
    "balance": 1,
    "req_id": 3
  },
  "msg_type": "balance",
  "req_id": 3
} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:16 [INFO]: ✅ Balance received: {
  "balance": 24856.64,
  "currency": "USD",
  "loginid": "VRTC3871036"
} {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:17 [INFO]: 🔚 Closing connection... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:57:17 [INFO]: 🔚 WebSocket closed {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:58:10 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:58:11 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: 🚀 Starting Deriv Bot Demo with your API key... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: 📊 Configuration loaded: {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - Environment: demo {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - Trading Enabled: true {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - Symbol: R_100 {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - Stake: 1 USD {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - Strategy: simple_trend {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: 🎯 Starting bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:14 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:44 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 21:59:51 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 21:59:52 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:32 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:34 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: 🚀 Starting Deriv Bot Demo with your API key... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: 📊 Configuration loaded: {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - Environment: demo {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - Trading Enabled: true {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - Symbol: R_100 {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - Stake: 1 USD {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - Strategy: simple_trend {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: 🎯 Starting bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:01:40 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:02:11 [ERROR]: Failed to get balance {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 22:02:11 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 22:02:41 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\src\\services\\DerivAPIService.ts:210:16)\n    at listOnTimeout (node:internal/timers:581:17)\n    at processTimers (node:internal/timers:519:7)"}
2025-06-27 22:03:14 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:15 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: 🚀 Starting Deriv Bot Demo with your API key... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: 📊 Configuration loaded: {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - Environment: demo {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - Trading Enabled: true {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - Symbol: R_100 {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - Stake: 1 USD {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - Strategy: simple_trend {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: - API Token: hEMSCZEt... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: 🎯 Starting bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:53 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:54 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:54 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:54 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 22:03:56 [INFO]: Subscribed to R_100 ticks {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:56 [INFO]: ✅ Bot started successfully! Monitoring for signals... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:03:56 [INFO]: 💡 Press Ctrl+C to stop the bot {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:04:55 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:04:56 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:06:36 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:06:37 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:08:17 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:08:18 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:09:58 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:10:00 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 🌐 Starting Deriv Bot Web Interface... {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: Web bot manager started successfully {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 🎯 Web interface started successfully! {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 📊 Dashboard available at: http://localhost:5173 {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 🔌 WebSocket server running on: http://localhost:3001 {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 💡 Use the web interface to start/stop the bot {"service":"deriv-bot","environment":"demo"}
2025-06-27 22:46:18 [INFO]: 🛑 Press Ctrl+C to stop the web server {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:07:58 [INFO]: 🛑 Shutting down web interface... {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:13 [INFO]: ================================================== {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:13 [INFO]: 🤖 Deriv Rise/Fall Trading Bot Starting... {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:13 [INFO]: ================================================== {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:13 [INFO]: Configuration loaded successfully {"service":"deriv-bot","environment":"demo","tradingEnabled":true,"symbol":"R_100","strategy":"simple_trend"}
2025-06-27 23:08:13 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:14 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:14 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:08:44 [ERROR]: Failed to get balance {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-06-27 23:08:44 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 23:09:14 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-06-27 23:09:26 [INFO]: ================================================== {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:26 [INFO]: 🤖 Deriv Rise/Fall Trading Bot Starting... {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:26 [INFO]: ================================================== {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:26 [INFO]: Configuration loaded successfully {"service":"deriv-bot","environment":"demo","tradingEnabled":true,"symbol":"R_100","strategy":"simple_trend"}
2025-06-27 23:09:26 [INFO]: Starting Deriv Bot... {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:26 [INFO]: Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:27 [INFO]: Authorized with Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:09:57 [ERROR]: Failed to get balance {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-06-27 23:09:57 [INFO]: Starting trading mode {"service":"deriv-bot","environment":"demo","symbol":"R_100"}
2025-06-27 23:10:27 [ERROR]: Failed to start bot {"service":"deriv-bot","environment":"demo","error":"Request timeout","stack":"Error: Request timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\Derivbot\\dist\\services\\DerivAPIService.js:137:24)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-06-27 23:30:42 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-27 23:30:44 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:27:08 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:27:09 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:09 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:27:09 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:11 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:11 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:27:11 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:15 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:15 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:27:15 [ERROR]: ❌ API Error: {"service":"deriv-bot","environment":"demo","error":"getaddrinfo ENOTFOUND ws.binaryws.com","stack":"Error: getaddrinfo ENOTFOUND ws.binaryws.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)"}
2025-06-28 00:27:24 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:29:04 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:29:05 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:30:45 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:30:46 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:32:26 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:32:28 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:34:08 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:34:09 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:35:49 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:35:50 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:37:30 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:37:31 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:39:11 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:39:12 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:40:52 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:40:54 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:42:34 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:42:35 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:44:15 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:44:16 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:45:56 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:45:57 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:47:37 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:47:38 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:49:18 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:49:19 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:51:00 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:51:01 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:52:41 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:52:42 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:54:22 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:54:23 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:56:03 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:56:04 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:57:44 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:57:46 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:59:26 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 00:59:27 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:01:07 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:01:08 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:02:48 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:02:49 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:04:29 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:04:30 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:06:10 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:06:11 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:07:51 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:07:53 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:09:33 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:09:34 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:11:14 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:11:15 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:12:55 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:12:56 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:14:36 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:14:38 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:16:18 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:16:19 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:17:59 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:18:00 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:19:40 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:19:41 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:21:21 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:21:22 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:23:02 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:23:04 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:24:44 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:24:45 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:26:25 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:26:26 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:28:06 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:28:08 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:29:48 [INFO]: ❌ Disconnected from Deriv API {"service":"deriv-bot","environment":"demo"}
2025-06-28 01:29:49 [INFO]: ✅ Connected to Deriv API {"service":"deriv-bot","environment":"demo"}
