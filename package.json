{"name": "deriv-rise-fall-bot", "version": "1.0.0", "description": "Automated trading bot for Deriv rise/fall options", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node src/index.ts", "demo": "ts-node demo.ts", "test-connection": "ts-node test-connection.ts", "simple-test": "ts-node simple-test.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["deriv", "trading", "bot", "rise-fall", "options", "automated-trading"], "author": "Your Name", "license": "MIT", "dependencies": {"ws": "^8.14.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/node": "^20.8.0", "@types/ws": "^8.5.8", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}}