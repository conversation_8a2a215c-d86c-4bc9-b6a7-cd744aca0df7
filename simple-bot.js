const WebSocket = require('ws');

console.log('🚀 Starting Simple Deriv Trading Bot...');
console.log('================================================');

let ws;
let isConnected = false;
let isAuthorized = false;
let balance = 0;
let currentPrice = 0;
let priceHistory = [];
let tradeCount = 0;

// Trading parameters
const SYMBOL = 'R_100';
const STAKE = 1;
const DURATION = 5; // 5 minutes
const TREND_PERIODS = 5; // Look at last 5 prices for trend

function connect() {
    console.log('🔌 Connecting to Deriv API...');
    ws = new WebSocket('wss://ws.binaryws.com/websockets/v3?app_id=1089');
    
    ws.on('open', function() {
        console.log('✅ Connected to Deriv API');
        isConnected = true;
        
        // Authorize
        const authMessage = {
            authorize: 'hEMSCZEtzrg5BzA',
            req_id: 1
        };
        
        console.log('🔐 Authorizing...');
        ws.send(JSON.stringify(authMessage));
    });
    
    ws.on('message', function(data) {
        const message = JSON.parse(data);
        
        if (message.msg_type === 'authorize') {
            if (message.error) {
                console.log('❌ Authorization failed:', message.error);
                return;
            }
            
            console.log('✅ Authorization successful!');
            console.log('💰 Balance: $' + message.authorize.balance);
            console.log('🏦 Account: ' + message.authorize.loginid);
            
            isAuthorized = true;
            balance = message.authorize.balance;
            
            // Start tick subscription
            startTradingMode();
            
        } else if (message.msg_type === 'tick') {
            handleTick(message.tick);
            
        } else if (message.msg_type === 'buy') {
            handleTradeResult(message);
            
        } else if (message.error) {
            console.log('❌ API Error:', message.error);
        }
    });
    
    ws.on('error', function(error) {
        console.log('❌ WebSocket error:', error);
        reconnect();
    });
    
    ws.on('close', function() {
        console.log('🔌 Connection closed');
        isConnected = false;
        isAuthorized = false;
        reconnect();
    });
}

function startTradingMode() {
    console.log('📊 Starting trading mode on ' + SYMBOL);
    
    const tickMessage = {
        ticks: SYMBOL,
        subscribe: 1,
        req_id: 2
    };
    
    ws.send(JSON.stringify(tickMessage));
}

function handleTick(tick) {
    currentPrice = tick.quote;
    const time = new Date(tick.epoch * 1000).toLocaleTimeString();
    
    console.log(`📈 ${SYMBOL}: $${currentPrice} at ${time}`);
    
    // Add to price history
    priceHistory.push(currentPrice);
    if (priceHistory.length > TREND_PERIODS) {
        priceHistory.shift(); // Keep only last N prices
    }
    
    // Analyze trend and make trading decision
    if (priceHistory.length >= TREND_PERIODS) {
        analyzeTrendAndTrade();
    }
}

function analyzeTrendAndTrade() {
    // Simple trend analysis
    const firstPrice = priceHistory[0];
    const lastPrice = priceHistory[priceHistory.length - 1];
    const trend = lastPrice > firstPrice ? 'UP' : 'DOWN';
    const trendStrength = Math.abs(lastPrice - firstPrice) / firstPrice;
    
    console.log(`🧠 Trend Analysis: ${trend} (Strength: ${(trendStrength * 100).toFixed(2)}%)`);
    
    // Only trade if trend is strong enough (> 0.1%)
    if (trendStrength > 0.001 && balance > STAKE) {
        const contractType = trend === 'UP' ? 'CALL' : 'PUT';
        placeTrade(contractType);
    }
}

function placeTrade(contractType) {
    tradeCount++;
    
    console.log(`🎯 Placing ${contractType} trade #${tradeCount}`);
    console.log(`💰 Stake: $${STAKE} | Duration: ${DURATION} min`);
    
    const tradeMessage = {
        buy: 1,
        price: STAKE,
        parameters: {
            contract_type: contractType,
            symbol: SYMBOL,
            duration: DURATION,
            duration_unit: 'm',
            currency: 'USD'
        },
        req_id: 100 + tradeCount
    };
    
    ws.send(JSON.stringify(tradeMessage));
}

function handleTradeResult(message) {
    if (message.error) {
        console.log('❌ Trade failed:', message.error);
        return;
    }
    
    const contract = message.buy;
    console.log('✅ Trade placed successfully!');
    console.log(`📋 Contract ID: ${contract.contract_id}`);
    console.log(`💵 Cost: $${contract.buy_price}`);
    console.log(`🎯 Payout: $${contract.payout}`);
    
    // Update balance
    balance -= contract.buy_price;
    console.log(`💰 New balance: $${balance.toFixed(2)}`);
}

function reconnect() {
    if (!isConnected) {
        console.log('🔄 Reconnecting in 5 seconds...');
        setTimeout(connect, 5000);
    }
}

// Start the bot
connect();

// Keep the bot running
process.on('SIGINT', function() {
    console.log('\n🛑 Stopping bot...');
    if (ws) {
        ws.close();
    }
    process.exit(0);
});

console.log('🤖 Simple Trading Bot is running!');
console.log('💡 Press Ctrl+C to stop');
console.log('================================================');
