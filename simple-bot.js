const WebSocket = require('ws');

console.log('🚀 Starting Simple Deriv Trading Bot...');
console.log('================================================');

let ws;
let isConnected = false;
let isAuthorized = false;
let balance = 0;
let currentPrice = 0;
// Multi-timeframe price history for enhanced trend analysis
let shortTermHistory = [];   // 30 seconds (15 data points)
let mediumTermHistory = [];  // 2 minutes (60 data points)
let longTermHistory = [];    // 10 minutes (300 data points)

let tradeCount = 0;
let isTrading = false; // Flag to prevent multiple simultaneous trades
let currentTradeId = null;

// RSI calculation variables
let rsiHistory = []; // For RSI calculation
const RSI_PERIOD = 14; // Standard RSI period

// Confidence tracking for momentum detection
let previousConfidence = 0;

// Trading parameters
const SYMBOL = 'R_100';
const STAKE = 100;
const DURATION = 1; // 1 minute
const SHORT_TERM_SIZE = 15;   // 30 seconds of data
const MEDIUM_TERM_SIZE = 60;  // 2 minutes of data
const LONG_TERM_SIZE = 300;   // 10 minutes of data
const TREND_THRESHOLD = 0.1;  // 0.1% minimum trend strength

function connect() {
    console.log('🔌 Connecting to Deriv API...');
    ws = new WebSocket('wss://ws.binaryws.com/websockets/v3?app_id=1089');
    
    ws.on('open', function() {
        console.log('✅ Connected to Deriv API');
        isConnected = true;
        
        // Authorize
        const authMessage = {
            authorize: 'hEMSCZEtzrg5BzA',
            req_id: 1
        };
        
        console.log('🔐 Authorizing...');
        ws.send(JSON.stringify(authMessage));
    });
    
    ws.on('message', function(data) {
        const message = JSON.parse(data);
        
        if (message.msg_type === 'authorize') {
            if (message.error) {
                console.log('❌ Authorization failed:', message.error);
                return;
            }
            
            console.log('✅ Authorization successful!');
            console.log('💰 Balance: $' + message.authorize.balance);
            console.log('🏦 Account: ' + message.authorize.loginid);
            
            isAuthorized = true;
            balance = message.authorize.balance;
            
            // Start tick subscription
            startTradingMode();
            
        } else if (message.msg_type === 'tick') {
            handleTick(message.tick);

        } else if (message.msg_type === 'proposal') {
            handleProposal(message);

        } else if (message.msg_type === 'buy') {
            handleTradeResult(message);

        } else if (message.error) {
            console.log('❌ API Error:', message.error);
        }
    });
    
    ws.on('error', function(error) {
        console.log('❌ WebSocket error:', error);
        reconnect();
    });
    
    ws.on('close', function() {
        console.log('🔌 Connection closed');
        isConnected = false;
        isAuthorized = false;
        reconnect();
    });
}

function startTradingMode() {
    console.log('📊 Starting trading mode on ' + SYMBOL);
    
    const tickMessage = {
        ticks: SYMBOL,
        subscribe: 1,
        req_id: 2
    };
    
    ws.send(JSON.stringify(tickMessage));
}

function handleTick(tick) {
    currentPrice = tick.quote;
    const time = new Date(tick.epoch * 1000).toLocaleTimeString();

    console.log(`📈 ${SYMBOL}: $${currentPrice} at ${time}`);

    // Add to all timeframe histories
    addToTimeframeHistories(currentPrice);

    // Analyze trend and make trading decision (only when we have enough data)
    if (shortTermHistory.length >= SHORT_TERM_SIZE) {
        analyzeMultiTimeframeAndTrade();
    }
}

function addToTimeframeHistories(price) {
    // Add to short-term history (30 seconds)
    shortTermHistory.push(price);
    if (shortTermHistory.length > SHORT_TERM_SIZE) {
        shortTermHistory.shift();
    }

    // Add to medium-term history (2 minutes)
    mediumTermHistory.push(price);
    if (mediumTermHistory.length > MEDIUM_TERM_SIZE) {
        mediumTermHistory.shift();
    }

    // Add to long-term history (10 minutes)
    longTermHistory.push(price);
    if (longTermHistory.length > LONG_TERM_SIZE) {
        longTermHistory.shift();
    }

    // Add to RSI history (maintain enough data for RSI calculation)
    rsiHistory.push(price);
    const maxRsiHistory = RSI_PERIOD * 3; // Keep 3x RSI period for better calculation
    if (rsiHistory.length > maxRsiHistory) {
        rsiHistory.shift();
    }
}

function analyzeMultiTimeframeAndTrade() {
    // Analyze each timeframe
    const shortAnalysis = analyzeTrendForTimeframe(shortTermHistory, "SHORT");
    const mediumAnalysis = analyzeTrendForTimeframe(mediumTermHistory, "MEDIUM");
    const longAnalysis = analyzeTrendForTimeframe(longTermHistory, "LONG");

    // Check timeframe alignment and calculate confidence
    const alignment = checkTimeframeAlignment(shortAnalysis, mediumAnalysis, longAnalysis);
    const confidence = calculateConfidenceScore(shortAnalysis, mediumAnalysis, longAnalysis);

    // Calculate RSI for overbought/oversold filtering
    const rsi = calculateRSI(rsiHistory); // Use dedicated RSI history

    // Check if momentum is building (confidence increasing)
    const momentumBuilding = confidence > previousConfidence;

    // Update previous confidence for next iteration
    previousConfidence = confidence;

    // Display multi-timeframe analysis with RSI
    console.log(`🧠 Multi-Timeframe Analysis:`);
    console.log(`   📊 Short (30s): ${shortAnalysis.direction} ${(shortAnalysis.strength * 100).toFixed(2)}%`);
    console.log(`   📊 Medium (2m): ${mediumAnalysis.direction} ${(mediumAnalysis.strength * 100).toFixed(2)}%`);
    console.log(`   📊 Long (10m): ${longAnalysis.direction} ${(longAnalysis.strength * 100).toFixed(2)}%`);
    console.log(`   🎯 Alignment: ${alignment} | Confidence: ${confidence}%`);
    console.log(`   📈 RSI: ${rsi.toFixed(1)} | Momentum: ${momentumBuilding ? '📈 Building' : '📉 Declining'}`);

    // NEW IMPROVED ENTRY LOGIC:
    // 1. Lower threshold (60% instead of 70%) BUT only when momentum is building
    // 2. RSI filter to avoid overbought/oversold conditions
    // 3. Strong alignment still required

    if ((alignment === "STRONG_BULLISH" || alignment === "STRONG_BEARISH") &&
        balance > STAKE && !isTrading) {

        const contractType = alignment === "STRONG_BULLISH" ? 'CALL' : 'PUT';
        const rsiOk = isRSIFavorable(rsi, contractType);

        // Early entry conditions (60% + momentum building + RSI favorable)
        if (confidence >= 60 && momentumBuilding && rsiOk) {
            console.log(`🚀 Early momentum entry - ${contractType} trade (${confidence}% confidence, RSI: ${rsi.toFixed(1)})`);
            placeTrade(contractType);
        }
        // High confidence entry (70%+ with RSI check)
        else if (confidence >= 70 && rsiOk) {
            console.log(`✅ High confidence signal - ${contractType} trade (${confidence}% confidence, RSI: ${rsi.toFixed(1)})`);
            placeTrade(contractType);
        }
        // RSI not favorable
        else if (!rsiOk) {
            const condition = contractType === 'CALL' ? 'overbought' : 'oversold';
            console.log(`⚠️ RSI ${condition} (${rsi.toFixed(1)}) - Skipping ${contractType} trade`);
        }
        // Confidence too low or momentum declining
        else {
            console.log(`⏸️ Waiting for momentum building - Confidence: ${confidence}%, Momentum: ${momentumBuilding ? 'Building' : 'Declining'}`);
        }
    } else if (confidence < 40) {
        console.log(`⏸️ Low confidence (${confidence}%) - No trade`);
    } else {
        console.log(`⏸️ Waiting for stronger alignment - Current: ${alignment}`);
    }
}

// RSI Calculation Function
function calculateRSI(prices) {
    if (prices.length < RSI_PERIOD + 1) {
        return 50; // Neutral RSI if not enough data
    }

    let gains = 0;
    let losses = 0;

    // Calculate initial average gain and loss
    for (let i = 1; i <= RSI_PERIOD; i++) {
        const change = prices[i] - prices[i - 1];
        if (change > 0) {
            gains += change;
        } else {
            losses += Math.abs(change);
        }
    }

    let avgGain = gains / RSI_PERIOD;
    let avgLoss = losses / RSI_PERIOD;

    // Calculate RSI for subsequent periods using smoothed averages
    for (let i = RSI_PERIOD + 1; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        const gain = change > 0 ? change : 0;
        const loss = change < 0 ? Math.abs(change) : 0;

        avgGain = ((avgGain * (RSI_PERIOD - 1)) + gain) / RSI_PERIOD;
        avgLoss = ((avgLoss * (RSI_PERIOD - 1)) + loss) / RSI_PERIOD;
    }

    if (avgLoss === 0) return 100; // Avoid division by zero

    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    return rsi;
}

// Check if RSI indicates overbought/oversold conditions
function isRSIFavorable(rsi, tradeDirection) {
    const OVERBOUGHT = 70;
    const OVERSOLD = 30;

    if (tradeDirection === 'CALL') {
        // For CALL trades, avoid overbought conditions
        return rsi < OVERBOUGHT;
    } else if (tradeDirection === 'PUT') {
        // For PUT trades, avoid oversold conditions
        return rsi > OVERSOLD;
    }

    return true; // Default to favorable if direction unclear
}

function analyzeTrendForTimeframe(history, timeframeName) {
    if (history.length < 5) {
        return { direction: 'UNKNOWN', strength: 0, timeframe: timeframeName };
    }

    const firstPrice = history[0];
    const lastPrice = history[history.length - 1];
    const direction = lastPrice > firstPrice ? 'UP' : 'DOWN';
    const strength = Math.abs(lastPrice - firstPrice) / firstPrice;

    // Calculate consistency (% of moves in trend direction)
    let consistentMoves = 0;
    for (let i = 1; i < history.length; i++) {
        const moveDirection = history[i] > history[i-1] ? 'UP' : 'DOWN';
        if (moveDirection === direction) {
            consistentMoves++;
        }
    }
    const consistency = consistentMoves / (history.length - 1);

    return {
        direction,
        strength,
        consistency,
        timeframe: timeframeName
    };
}

function checkTimeframeAlignment(short, medium, long) {
    const shortDir = short.direction;
    const mediumDir = medium.direction;
    const longDir = long.direction;

    // Strong alignment: All timeframes agree
    if (shortDir === 'UP' && mediumDir === 'UP' && longDir === 'UP') {
        return 'STRONG_BULLISH';
    }
    if (shortDir === 'DOWN' && mediumDir === 'DOWN' && longDir === 'DOWN') {
        return 'STRONG_BEARISH';
    }

    // Medium alignment: 2 out of 3 agree
    const upCount = [shortDir, mediumDir, longDir].filter(dir => dir === 'UP').length;
    const downCount = [shortDir, mediumDir, longDir].filter(dir => dir === 'DOWN').length;

    if (upCount === 2) return 'MEDIUM_BULLISH';
    if (downCount === 2) return 'MEDIUM_BEARISH';

    // Weak or conflicting signals
    return 'CONFLICTED';
}

function calculateConfidenceScore(short, medium, long) {
    let score = 0;

    // Strength component (0-40 points)
    const avgStrength = (short.strength + medium.strength + long.strength) / 3;
    if (avgStrength > 0.004) score += 40;      // >0.4%
    else if (avgStrength > 0.002) score += 30; // >0.2%
    else if (avgStrength > 0.001) score += 20; // >0.1%
    else score += 10;

    // Consistency component (0-30 points)
    const avgConsistency = (short.consistency + medium.consistency + long.consistency) / 3;
    if (avgConsistency > 0.8) score += 30;      // >80%
    else if (avgConsistency > 0.7) score += 20; // >70%
    else if (avgConsistency > 0.6) score += 10; // >60%

    // Alignment component (0-30 points)
    const alignment = checkTimeframeAlignment(short, medium, long);
    if (alignment.includes('STRONG')) score += 30;
    else if (alignment.includes('MEDIUM')) score += 15;

    return Math.min(100, score);
}

function placeTrade(contractType) {
    tradeCount++;
    isTrading = true; // Set trading flag to prevent multiple trades

    console.log(`🎯 Placing ${contractType} trade #${tradeCount}`);
    console.log(`💰 Stake: $${STAKE} | Duration: ${DURATION} min`);
    console.log(`⏳ Trading in progress... waiting for completion`);

    // Step 1: Get proposal (quote)
    const proposalMessage = {
        proposal: 1,
        amount: STAKE * 2, // Use payout amount (roughly 2x stake for binary options)
        basis: 'payout',
        contract_type: contractType,
        symbol: SYMBOL,
        duration: DURATION,
        duration_unit: 'm',
        currency: 'USD',
        req_id: 200 + tradeCount
    };

    ws.send(JSON.stringify(proposalMessage));
}

function handleProposal(message) {
    if (message.error) {
        console.log('❌ Proposal failed:', message.error);
        isTrading = false; // Reset trading flag on proposal failure
        console.log('🔄 Ready for next trading opportunity');
        return;
    }

    const proposal = message.proposal;
    console.log(`💡 Proposal received: $${proposal.ask_price} for ${proposal.display_name}`);

    // Step 2: Buy the contract using the proposal ID and actual price
    const buyMessage = {
        buy: proposal.id,
        price: proposal.ask_price,
        req_id: 300 + tradeCount
    };

    ws.send(JSON.stringify(buyMessage));
}

function handleTradeResult(message) {
    if (message.error) {
        console.log('❌ Trade failed:', message.error);
        isTrading = false; // Reset trading flag on failure
        console.log('🔄 Ready for next trading opportunity');
        return;
    }

    const contract = message.buy;
    console.log('✅ Trade placed successfully!');
    console.log(`📋 Contract ID: ${contract.contract_id}`);
    console.log(`💵 Cost: $${contract.buy_price}`);
    console.log(`🎯 Payout: $${contract.payout}`);
    currentTradeId = contract.contract_id;

    // Update balance
    balance -= contract.buy_price;
    console.log(`💰 New balance: $${balance.toFixed(2)}`);

    // Set a timer to reset trading flag after contract duration
    setTimeout(() => {
        isTrading = false;
        currentTradeId = null;
        console.log('⏰ Trade completed - Ready for next opportunity');
    }, DURATION * 60 * 1000); // Convert minutes to milliseconds
}

function reconnect() {
    if (!isConnected) {
        console.log('🔄 Reconnecting in 5 seconds...');
        setTimeout(connect, 5000);
    }
}

// Start the bot
connect();

// Keep the bot running
process.on('SIGINT', function() {
    console.log('\n🛑 Stopping bot...');
    if (ws) {
        ws.close();
    }
    process.exit(0);
});

console.log('🤖 Simple Trading Bot is running!');
console.log('💡 Press Ctrl+C to stop');
console.log('================================================');
