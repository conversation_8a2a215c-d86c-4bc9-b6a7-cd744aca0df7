#!/usr/bin/env ts-node

import WebSocket from 'ws';
import { Logger } from './src/utils/Logger';

const logger = Logger.getInstance();

async function simpleTest() {
  logger.info('🔍 Simple WebSocket Test...');
  
  const apiToken = 'hEMSCZEtzrg5BzA';
  const appId = '1089';
  const url = `wss://ws.binaryws.com/websockets/v3?app_id=${appId}&l=en`;
  
  logger.info(`📡 Connecting to: ${url}`);
  
  const ws = new WebSocket(url);
  
  ws.on('open', () => {
    logger.info('✅ WebSocket connected');

    // Test 1: Get active symbols (no auth required)
    logger.info('📋 Testing active symbols...');
    const symbolsMessage = {
      active_symbols: 'brief',
      product_type: 'basic',
      req_id: 1
    };
    logger.info(`📤 Sending: ${JSON.stringify(symbolsMessage)}`);
    ws.send(JSON.stringify(symbolsMessage));

    // Test 2: Try authorization
    setTimeout(() => {
      logger.info('🔐 Testing authorization...');
      const authMessage = {
        authorize: apiToken,
        req_id: 2
      };
      logger.info(`📤 Sending: ${JSON.stringify(authMessage)}`);
      ws.send(JSON.stringify(authMessage));
    }, 2000);
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      logger.info(`📨 Received message: ${JSON.stringify(message, null, 2)}`);

      // Log any errors in the response
      if (message.error) {
        logger.error(`❌ API Error: ${JSON.stringify(message.error, null, 2)}`);
      }
      
      if (message.req_id === 1 && message.active_symbols) {
        logger.info(`✅ Active symbols: ${message.active_symbols.length} symbols received`);
      }
      
      if (message.req_id === 2) {
        if (message.authorize) {
          logger.info('✅ Authorization successful!');
          logger.info(`Account info: ${JSON.stringify(message.authorize, null, 2)}`);
          
          // Test balance after successful auth
          setTimeout(() => {
            logger.info('💰 Testing balance...');
            ws.send(JSON.stringify({
              balance: 1,
              req_id: 3
            }));
          }, 1000);
        } else if (message.error) {
          logger.error('❌ Authorization failed:', message.error);
        }
      }
      
      if (message.req_id === 3) {
        if (message.balance) {
          logger.info(`✅ Balance received: ${JSON.stringify(message.balance, null, 2)}`);
        } else if (message.error) {
          logger.error('❌ Balance failed:', message.error);
        }
        
        // Close connection after balance test
        setTimeout(() => {
          logger.info('🔚 Closing connection...');
          ws.close();
        }, 1000);
      }
      
    } catch (error) {
      logger.error('❌ Failed to parse message:', error instanceof Error ? error : new Error(String(error)));
    }
  });
  
  ws.on('error', (error) => {
    logger.error('❌ WebSocket error:', error);
  });
  
  ws.on('close', () => {
    logger.info('🔚 WebSocket closed');
    process.exit(0);
  });
  
  // Timeout after 30 seconds
  setTimeout(() => {
    logger.warn('⏰ Test timeout, closing...');
    ws.close();
  }, 30000);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('🔄 Received SIGINT, shutting down...');
  process.exit(0);
});

// Run the test
simpleTest().catch((error) => {
  logger.error('💥 Test crashed:', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
});
