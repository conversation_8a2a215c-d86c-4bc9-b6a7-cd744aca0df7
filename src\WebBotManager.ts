import { DerivBot } from './bot/DerivBot';
import { WebSocketServer } from './server/WebSocketServer';
import { ConfigManager } from './config/ConfigManager';
import { Logger } from './utils/Logger';

export class WebBotManager {
  private bot: DerivBot | null = null;
  private webServer: WebSocketServer;
  private config: ConfigManager;
  private logger: Logger;
  private isRunning: boolean = false;
  private stats = {
    totalTrades: 0,
    winRate: 0,
    totalProfit: 0,
    todayProfit: 0,
    activeTrades: 0,
    balance: 0
  };

  constructor() {
    this.config = ConfigManager.getInstance();
    this.logger = Logger.getInstance();
    
    // Initialize WebSocket server
    this.webServer = new WebSocketServer({
      port: 3001,
      corsOrigin: 'http://localhost:5173' // Vite dev server default port
    });

    this.setupWebServerHandlers();
  }

  private setupWebServerHandlers(): void {
    // Handle client connections
    this.webServer.on('client_connected', (clientId: string) => {
      this.logger.info(`Web client connected: ${clientId}`);
      // Send current status to new client
      this.sendCurrentStatus();
    });

    // Handle bot control commands
    this.webServer.on('start_bot_command', async (clientId: string) => {
      this.logger.info(`Start bot command received from client: ${clientId}`);
      await this.startBot();
    });

    this.webServer.on('stop_bot_command', async (clientId: string) => {
      this.logger.info(`Stop bot command received from client: ${clientId}`);
      await this.stopBot();
    });

    this.webServer.on('status_request', () => {
      this.sendCurrentStatus();
    });
  }

  private setupBotEventHandlers(): void {
    if (!this.bot) return;

    // Forward bot events to web clients
    this.bot.on('tick', (tickData: any) => {
      this.webServer.broadcastTickData({
        symbol: tickData.symbol,
        quote: tickData.quote,
        ask: tickData.ask,
        bid: tickData.bid,
        epoch: tickData.epoch,
        timestamp: new Date().toISOString()
      });
    });

    this.bot.on('signal', (signal: any) => {
      this.webServer.broadcastTradeSignal(signal);
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'info',
        message: `${signal.type} signal detected at ${signal.price}`,
        timestamp: new Date().toISOString(),
        data: signal
      });
    });

    this.bot.on('trade_placed', (trade: any) => {
      this.stats.activeTrades++;
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'info',
        message: `${trade.type} trade placed - Stake: $${trade.stake}`,
        timestamp: new Date().toISOString(),
        data: trade
      });
      this.sendStats();
    });

    this.bot.on('trade_result', (result: any) => {
      this.stats.totalTrades++;
      this.stats.activeTrades = Math.max(0, this.stats.activeTrades - 1);
      
      if (result.profit) {
        this.stats.totalProfit += result.profit;
        this.stats.todayProfit += result.profit;
      }
      
      // Calculate win rate
      if (this.stats.totalTrades > 0) {
        // This is simplified - in a real implementation, you'd track wins/losses separately
        this.stats.winRate = result.status === 'WON' ? 
          ((this.stats.winRate * (this.stats.totalTrades - 1)) + 100) / this.stats.totalTrades :
          (this.stats.winRate * (this.stats.totalTrades - 1)) / this.stats.totalTrades;
      }

      this.webServer.broadcastTradeResult(result);
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: result.status === 'WON' ? 'success' : 'error',
        message: `Trade ${result.status}: ${result.profit ? `$${result.profit.toFixed(2)} profit` : 'No profit'}`,
        timestamp: new Date().toISOString(),
        data: result
      });
      
      this.sendStats();
    });

    this.bot.on('balance_update', (balance: any) => {
      this.stats.balance = balance;
      this.sendCurrentStatus();
      this.sendStats();
    });

    this.bot.on('error', (error: any) => {
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'error',
        message: `Bot error: ${error.message}`,
        timestamp: new Date().toISOString(),
        data: { error: error.message, stack: error.stack }
      });
    });
  }

  private sendCurrentStatus(): void {
    const status = {
      isRunning: this.isRunning,
      isConnected: this.bot?.isConnected() || false,
      isTrading: this.bot?.isTrading() || false,
      balance: this.stats.balance,
      symbol: this.config.getTradingConfig().symbol,
      lastUpdate: new Date().toISOString()
    };

    this.webServer.broadcastBotStatus(status);
  }

  private sendStats(): void {
    this.webServer.broadcastStats(this.stats);
  }

  public async startBot(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Bot is already running');
      return;
    }

    try {
      this.logger.info('Starting trading bot...');
      
      // Create new bot instance
      this.bot = new DerivBot();
      this.setupBotEventHandlers();
      
      // Start the bot
      await this.bot.start();
      this.isRunning = true;
      
      this.logger.info('Trading bot started successfully');
      this.sendCurrentStatus();
      
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'success',
        message: 'Trading bot started successfully',
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      this.logger.error('Failed to start bot:', error instanceof Error ? error : new Error(String(error)));
      this.isRunning = false;
      this.sendCurrentStatus();
      
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'error',
        message: `Failed to start bot: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  public async stopBot(): Promise<void> {
    if (!this.isRunning || !this.bot) {
      this.logger.warn('Bot is not running');
      return;
    }

    try {
      this.logger.info('Stopping trading bot...');
      
      await this.bot.stop();
      this.bot = null;
      this.isRunning = false;
      
      this.logger.info('Trading bot stopped successfully');
      this.sendCurrentStatus();
      
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'info',
        message: 'Trading bot stopped',
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      this.logger.error('Error stopping bot:', error instanceof Error ? error : new Error(String(error)));
      
      this.webServer.broadcastLog({
        id: Date.now().toString(),
        level: 'error',
        message: `Error stopping bot: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  public async start(): Promise<void> {
    try {
      await this.webServer.start();
      this.logger.info('Web bot manager started successfully');
      
      // Send initial status
      this.sendCurrentStatus();
      this.sendStats();
      
    } catch (error) {
      this.logger.error('Failed to start web server:', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      if (this.isRunning) {
        await this.stopBot();
      }
      await this.webServer.stop();
      this.logger.info('Web bot manager stopped');
    } catch (error) {
      this.logger.error('Error stopping web bot manager:', error instanceof Error ? error : new Error(String(error)));
    }
  }
}
