import { ConfigManager } from '../config/ConfigManager';

// Mock environment variables
const mockEnv = {
  DERIV_APP_ID: 'test_app_id',
  DERIV_API_TOKEN: 'test_token',
  DEFAULT_STAKE: '25',
  DEFAULT_CURRENCY: 'EUR',
  DEFAULT_SYMBOL: 'R_50',
  MAX_DAILY_LOSS: '200',
  TRADING_ENABLED: 'true',
  STRATEGY_TYPE: 'ma_crossover',
};

describe('ConfigManager', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Set mock environment
    Object.assign(process.env, mockEnv);
    
    // Reset singleton instance
    (ConfigManager as any).instance = undefined;
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('getInstance', () => {
    it('should return a singleton instance', () => {
      const instance1 = ConfigManager.getInstance();
      const instance2 = ConfigManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('getDerivAPIConfig', () => {
    it('should return correct API configuration', () => {
      const config = ConfigManager.getInstance();
      const apiConfig = config.getDerivAPIConfig();
      
      expect(apiConfig.appId).toBe('test_app_id');
      expect(apiConfig.apiToken).toBe('test_token');
    });

    it('should throw error when DERIV_APP_ID is missing', () => {
      delete process.env.DERIV_APP_ID;
      
      expect(() => {
        ConfigManager.getInstance();
      }).toThrow('DERIV_APP_ID is required');
    });
  });

  describe('getBotConfig', () => {
    it('should return correct bot configuration', () => {
      const config = ConfigManager.getInstance();
      const botConfig = config.getBotConfig();
      
      expect(botConfig.trading.stake).toBe(25);
      expect(botConfig.trading.currency).toBe('EUR');
      expect(botConfig.trading.symbol).toBe('R_50');
      expect(botConfig.risk.maxDailyLoss).toBe(200);
      expect(botConfig.strategy.type).toBe('ma_crossover');
    });
  });

  describe('isTradingEnabled', () => {
    it('should return true when TRADING_ENABLED is true', () => {
      const config = ConfigManager.getInstance();
      expect(config.isTradingEnabled()).toBe(true);
    });

    it('should return false when TRADING_ENABLED is false', () => {
      process.env.TRADING_ENABLED = 'false';
      const config = ConfigManager.getInstance();
      expect(config.isTradingEnabled()).toBe(false);
    });
  });

  describe('updateTradingConfig', () => {
    it('should update trading configuration', () => {
      const config = ConfigManager.getInstance();
      
      config.updateTradingConfig({
        stake: 50,
        symbol: 'R_75',
      });
      
      const tradingConfig = config.getTradingConfig();
      expect(tradingConfig.stake).toBe(50);
      expect(tradingConfig.symbol).toBe('R_75');
      expect(tradingConfig.currency).toBe('EUR'); // Should remain unchanged
    });
  });

  describe('validation', () => {
    it('should throw error for invalid stake', () => {
      process.env.DEFAULT_STAKE = '0';
      
      expect(() => {
        ConfigManager.getInstance();
      }).toThrow('Configuration validation failed');
    });

    it('should throw error for invalid duration unit', () => {
      process.env.DEFAULT_DURATION_UNIT = 'invalid';
      
      expect(() => {
        ConfigManager.getInstance();
      }).toThrow('Configuration validation failed');
    });

    it('should throw error for invalid stop loss percentage', () => {
      process.env.STOP_LOSS_PERCENTAGE = '150';
      
      expect(() => {
        ConfigManager.getInstance();
      }).toThrow('Configuration validation failed');
    });
  });

  describe('exportConfig and importConfig', () => {
    it('should export and import configuration correctly', () => {
      const config = ConfigManager.getInstance();
      const exported = config.exportConfig();
      
      expect(() => JSON.parse(exported)).not.toThrow();
      
      const parsedConfig = JSON.parse(exported);
      expect(parsedConfig.trading.stake).toBe(25);
      expect(parsedConfig.trading.currency).toBe('EUR');
    });

    it('should throw error for invalid JSON import', () => {
      const config = ConfigManager.getInstance();
      
      expect(() => {
        config.importConfig('invalid json');
      }).toThrow('Failed to import configuration');
    });
  });
});
