import { DerivBot } from '../bot/DerivBot';
import { ConfigManager } from '../config/ConfigManager';
import { MockDerivAPIService } from '../test-utils/MockDerivAPIService';
import { Logger } from '../utils/Logger';

// Mock environment variables for testing
const mockEnv = {
  DERIV_APP_ID: 'test_app_id',
  DERIV_API_TOKEN: 'test_token',
  DERIV_API_URL: 'wss://ws.derivws.com/websockets/v3',
  DEFAULT_STAKE: '10',
  DEFAULT_CURRENCY: 'USD',
  DEFAULT_SYMBOL: 'R_100',
  MAX_DAILY_LOSS: '100',
  TRADING_ENABLED: 'false', // Disable actual trading for tests
  STRATEGY_TYPE: 'simple_trend',
  SIGNAL_THRESHOLD: '0.7',
  LOOKBACK_PERIODS: '5',
};

describe('DerivBot Integration Tests', () => {
  let bot: DerivBot;
  let mockAPI: MockDerivAPIService;
  let logger: Logger;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Set mock environment
    Object.assign(process.env, mockEnv);
    
    // Reset singleton instances
    (ConfigManager as any).instance = undefined;
    
    // Create instances
    ConfigManager.getInstance();
    logger = Logger.getInstance();
    mockAPI = new MockDerivAPIService();

    // Create bot with mock API
    bot = new DerivBot();
    // Replace the API service with our mock
    (bot as any).api = mockAPI;
    // Re-setup event listeners for the mock API
    (bot as any).setupEventListeners();
  });

  afterEach(async () => {
    // Cleanup
    if (bot) {
      await bot.stop();
    }
    if (mockAPI) {
      await mockAPI.disconnect();
    }
    
    // Restore original environment
    process.env = originalEnv;
  });

  describe('Bot Lifecycle', () => {
    it('should start and stop successfully', async () => {
      await bot.start();

      // Wait a bit for the bot to initialize
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(mockAPI.isConnected()).toBe(true);
      
      await bot.stop();
      expect(mockAPI.isConnected()).toBe(false);
    }, 10000);

    it('should handle connection errors gracefully', async () => {
      const errorSpy = jest.spyOn(logger, 'error');

      // Add error handler to bot to prevent unhandled error
      bot.on('error', (_error) => {
        // Error is handled by the bot
      });

      // Start the bot and wait for it to be fully started
      await bot.start();

      // Wait a bit to ensure bot is fully initialized
      await new Promise(resolve => setTimeout(resolve, 100));

      // Simulate connection error
      mockAPI.simulateConnectionError();

      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 300));

      expect(errorSpy).toHaveBeenCalled();

      await bot.stop();
    }, 10000);
  });

  describe('Tick Processing', () => {
    it('should process tick data and generate signals', async () => {
      const signalSpy = jest.fn();
      bot.on('signal_generated', signalSpy);

      await bot.start();

      // Generate enough trending tick data to trigger signals (need at least 10 for lookback)
      const basePrice = 100;
      for (let i = 0; i < 15; i++) {
        mockAPI.triggerTick('R_100', basePrice + i * 0.2); // Stronger trend
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Wait for signal processing
      await new Promise(resolve => setTimeout(resolve, 500));

      // Should have generated at least one signal due to upward trend
      expect(signalSpy).toHaveBeenCalled();

      await bot.stop();
    }, 15000);

    it('should respect signal confidence threshold', async () => {
      const signalSpy = jest.fn();
      bot.on('signal_generated', signalSpy);
      
      await bot.start();
      
      // Generate sideways market data (low confidence signals)
      const basePrice = 100;
      for (let i = 0; i < 10; i++) {
        const randomChange = (Math.random() - 0.5) * 0.01;
        mockAPI.triggerTick('R_100', basePrice + randomChange);
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      // Wait for signal processing
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Should not generate signals due to low confidence
      expect(signalSpy).not.toHaveBeenCalled();
      
      await bot.stop();
    }, 15000);
  });

  describe('Risk Management', () => {
    it('should respect daily loss limits', async () => {
      // Set a very low daily loss limit and enable trading
      process.env['MAX_DAILY_LOSS'] = '1';
      process.env['TRADING_ENABLED'] = 'true';
      (ConfigManager as any).instance = undefined;
      ConfigManager.getInstance();
      
      bot = new DerivBot();
      (bot as any).api = mockAPI;

      // Re-setup event listeners for the mock API
      (bot as any).setupEventListeners();

      // Simulate a loss that exceeds the limit
      (bot as any).dailyProfit = -2;
      
      const tradingStoppedSpy = jest.fn();
      bot.on('trading_stopped', tradingStoppedSpy);
      
      await bot.start();
      
      // Generate enough ticks to trigger signals and risk management
      for (let i = 0; i < 15; i++) {
        mockAPI.triggerTick('R_100', 100 + i * 0.3);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Should have stopped trading due to daily loss limit
      expect(tradingStoppedSpy).toHaveBeenCalled();
      
      await bot.stop();
    }, 15000);
  });

  describe('Statistics and Monitoring', () => {
    it('should track and report statistics', async () => {
      await bot.start();
      
      // Generate some tick data
      for (let i = 0; i < 5; i++) {
        mockAPI.triggerTick('R_100', 100 + i * 0.1);
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const stats = bot.getStats();

      expect(stats).toHaveProperty('totalTrades');
      expect(stats).toHaveProperty('winRate');
      expect(stats).toHaveProperty('totalProfit');
      expect(stats).toHaveProperty('dailyProfit');
      expect(stats).toHaveProperty('consecutiveLosses');
      expect(stats).toHaveProperty('isActive');
      expect(stats).toHaveProperty('lastTradeTime');
      
      await bot.stop();
    }, 10000);

    it('should reset daily statistics at midnight', async () => {
      await bot.start();
      
      // Simulate some activity
      (bot as any).dailyProfit = 50;
      
      // Manually trigger daily reset
      (bot as any).checkDailyReset = jest.fn(() => {
        (bot as any).dailyProfit = 0;
        (bot as any).lastResetDate = new Date().toDateString();
      });
      
      (bot as any).checkDailyReset();
      
      const stats = bot.getStats();
      expect(stats.totalTrades).toBeGreaterThanOrEqual(0);
      
      await bot.stop();
    }, 10000);
  });

  describe('Strategy Integration', () => {
    it('should work with simple trend strategy', async () => {
      process.env['STRATEGY_TYPE'] = 'simple_trend';
      (ConfigManager as any).instance = undefined;
      ConfigManager.getInstance();
      
      bot = new DerivBot();
      (bot as any).api = mockAPI;
      
      const signalSpy = jest.fn();
      bot.on('signal_generated', signalSpy);
      
      await bot.start();
      
      // Generate strong upward trend
      for (let i = 0; i < 15; i++) {
        mockAPI.triggerTick('R_100', 100 + i * 0.2);
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      if (signalSpy.mock.calls.length > 0) {
        const signal = signalSpy.mock.calls[0][0];
        expect(signal.direction).toBe('CALL');
        expect(signal.confidence).toBeGreaterThan(0.7);
      }
      
      await bot.stop();
    }, 15000);

    it('should work with moving average crossover strategy', async () => {
      process.env['STRATEGY_TYPE'] = 'ma_crossover';
      (ConfigManager as any).instance = undefined;
      ConfigManager.getInstance();
      
      bot = new DerivBot();
      (bot as any).api = mockAPI;

      // Re-setup event listeners for the mock API
      (bot as any).setupEventListeners();

      const signalSpy = jest.fn();
      bot.on('signal_generated', signalSpy);
      
      await bot.start();
      
      // Generate data that should create a crossover
      // First declining trend (need at least 15 ticks for long MA)
      for (let i = 0; i < 25; i++) {
        mockAPI.triggerTick('R_100', 110 - i * 0.1);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Then rising trend to create crossover
      for (let i = 0; i < 20; i++) {
        mockAPI.triggerTick('R_100', 85 + i * 0.4);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Should have generated signals due to crossover
      expect(signalSpy).toHaveBeenCalled();
      
      await bot.stop();
    }, 20000);
  });
});
