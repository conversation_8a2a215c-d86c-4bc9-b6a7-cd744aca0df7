import { SimpleTrendStrategy, MovingAverageCrossoverStrategy } from '../strategies/TradingStrategy';
import { TickData } from '../types';

// Helper function to create mock tick data
const createMockTick = (quote: number, symbol: string = 'R_100', epoch: number = Date.now()): TickData => ({
  tick: {
    ask: quote + 0.001,
    bid: quote - 0.001,
    epoch,
    id: `tick_${epoch}`,
    pip_size: 4,
    quote,
    symbol,
  },
});

describe('SimpleTrendStrategy', () => {
  let strategy: SimpleTrendStrategy;

  beforeEach(() => {
    strategy = new SimpleTrendStrategy(0.6, 10);
  });

  describe('addTick', () => {
    it('should add tick to history', () => {
      const tick = createMockTick(100.5);
      strategy.addTick(tick);
      
      expect(strategy['tickHistory']).toHaveLength(1);
      expect(strategy['tickHistory'][0]).toBe(tick);
    });

    it('should maintain maximum history size', () => {
      // Add more ticks than maxHistorySize
      for (let i = 0; i < 150; i++) {
        strategy.addTick(createMockTick(100 + i));
      }
      
      expect(strategy['tickHistory'].length).toBeLessThanOrEqual(strategy['maxHistorySize']);
    });
  });

  describe('generateSignal', () => {
    it('should return null when insufficient data', () => {
      // Add only a few ticks (less than lookback period)
      for (let i = 0; i < 5; i++) {
        strategy.addTick(createMockTick(100 + i));
      }
      
      const signal = strategy.generateSignal();
      expect(signal).toBeNull();
    });

    it('should generate CALL signal for upward trend', () => {
      // Create upward trending data
      for (let i = 0; i < 15; i++) {
        strategy.addTick(createMockTick(100 + i * 0.1));
      }
      
      const signal = strategy.generateSignal();
      expect(signal).not.toBeNull();
      expect(signal?.direction).toBe('CALL');
      expect(signal?.confidence).toBeGreaterThan(0);
    });

    it('should generate PUT signal for downward trend', () => {
      // Create downward trending data
      for (let i = 0; i < 15; i++) {
        strategy.addTick(createMockTick(100 - i * 0.1));
      }
      
      const signal = strategy.generateSignal();
      expect(signal).not.toBeNull();
      expect(signal?.direction).toBe('PUT');
      expect(signal?.confidence).toBeGreaterThan(0);
    });

    it('should return null for sideways market', () => {
      // Create sideways market data
      for (let i = 0; i < 15; i++) {
        strategy.addTick(createMockTick(100 + (Math.random() - 0.5) * 0.01));
      }
      
      const signal = strategy.generateSignal();
      // Might be null due to low confidence or sideways trend
      if (signal) {
        expect(signal.confidence).toBeLessThan(0.9);
      }
    });
  });

  describe('helper methods', () => {
    beforeEach(() => {
      // Add some test data
      for (let i = 0; i < 20; i++) {
        strategy.addTick(createMockTick(100 + i));
      }
    });

    it('should calculate SMA correctly', () => {
      const sma = strategy['calculateSMA'](5);
      expect(sma).not.toBeNull();
      expect(typeof sma).toBe('number');
    });

    it('should calculate EMA correctly', () => {
      const ema = strategy['calculateEMA'](5);
      expect(ema).not.toBeNull();
      expect(typeof ema).toBe('number');
    });

    it('should calculate price change correctly', () => {
      const priceChange = strategy['calculatePriceChange'](5);
      expect(priceChange).not.toBeNull();
      expect(typeof priceChange).toBe('number');
    });

    it('should detect trend correctly', () => {
      const trend = strategy['detectTrend'](10);
      expect(trend).toBe('up'); // Since we added increasing prices
    });

    it('should calculate volatility correctly', () => {
      const volatility = strategy['calculateVolatility'](10);
      expect(volatility).not.toBeNull();
      expect(typeof volatility).toBe('number');
      expect(volatility).toBeGreaterThanOrEqual(0);
    });
  });

  describe('reset', () => {
    it('should clear tick history', () => {
      strategy.addTick(createMockTick(100));
      strategy.addTick(createMockTick(101));
      
      expect(strategy['tickHistory']).toHaveLength(2);
      
      strategy.reset();
      
      expect(strategy['tickHistory']).toHaveLength(0);
    });
  });
});

describe('MovingAverageCrossoverStrategy', () => {
  let strategy: MovingAverageCrossoverStrategy;

  beforeEach(() => {
    strategy = new MovingAverageCrossoverStrategy(5, 15, 0.7);
  });

  describe('generateSignal', () => {
    it('should return null when insufficient data', () => {
      for (let i = 0; i < 10; i++) {
        strategy.addTick(createMockTick(100 + i));
      }
      
      const signal = strategy.generateSignal();
      expect(signal).toBeNull();
    });

    it('should detect bullish crossover', () => {
      // Create data where short MA will cross above long MA
      // First, add data for long MA to be above short MA
      for (let i = 0; i < 20; i++) {
        strategy.addTick(createMockTick(100 - i * 0.1));
      }
      
      // Then add data that will cause short MA to cross above
      for (let i = 0; i < 10; i++) {
        strategy.addTick(createMockTick(80 + i * 0.5));
      }
      
      const signal = strategy.generateSignal();
      // Note: This test might need adjustment based on exact crossover logic
      if (signal) {
        expect(signal.direction).toBe('CALL');
      }
    });

    it('should detect bearish crossover', () => {
      // Create data where short MA will cross below long MA
      // First, add data for short MA to be above long MA
      for (let i = 0; i < 20; i++) {
        strategy.addTick(createMockTick(100 + i * 0.1));
      }
      
      // Then add data that will cause short MA to cross below
      for (let i = 0; i < 10; i++) {
        strategy.addTick(createMockTick(120 - i * 0.5));
      }
      
      const signal = strategy.generateSignal();
      // Note: This test might need adjustment based on exact crossover logic
      if (signal) {
        expect(signal.direction).toBe('PUT');
      }
    });
  });

  describe('moving average calculations', () => {
    beforeEach(() => {
      // Add sufficient data for calculations
      for (let i = 0; i < 25; i++) {
        strategy.addTick(createMockTick(100 + i));
      }
    });

    it('should calculate short MA correctly', () => {
      const shortMA = strategy['calculateSMA'](5);
      expect(shortMA).not.toBeNull();
      expect(typeof shortMA).toBe('number');
    });

    it('should calculate long MA correctly', () => {
      const longMA = strategy['calculateSMA'](15);
      expect(longMA).not.toBeNull();
      expect(typeof longMA).toBe('number');
    });

    it('should calculate MA at specific index', () => {
      const maAtIndex = strategy['calculateSMAAtIndex'](5, 1);
      expect(maAtIndex).not.toBeNull();
      expect(typeof maAtIndex).toBe('number');
    });
  });
});
