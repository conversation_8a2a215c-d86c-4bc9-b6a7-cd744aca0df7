import { EventEmitter } from 'events';
import { DerivAPIService } from '../services/DerivAPIService';
import { ConfigManager } from '../config/ConfigManager';
import { Logger, PerformanceMonitor } from '../utils/Logger';
import { TradingStrategy, SimpleTrendStrategy, MovingAverageCrossoverStrategy } from '../strategies/TradingStrategy';
import {
  TickData,
  TradingSignal,
  TradeResult,
  BotStats,
  ProposalRequest,
  BuyRequest,
} from '../types';

export class DerivBot extends EventEmitter {
  private api: DerivAPIService;
  private config: ConfigManager;
  private logger: Logger;
  private monitor: PerformanceMonitor;
  private strategy: TradingStrategy;
  
  private isRunning = false;
  private stats: BotStats = {
    totalTrades: 0,
    winRate: 0,
    totalProfit: 0,
    dailyProfit: 0,
    consecutiveLosses: 0,
    isActive: false,
    lastTradeTime: 0,
  };
  
  private tradeHistory: TradeResult[] = [];
  private dailyProfit = 0;
  private lastResetDate = new Date().toDateString();

  constructor() {
    super();
    this.config = ConfigManager.getInstance();
    this.logger = Logger.getInstance();
    this.monitor = PerformanceMonitor.getInstance();
    
    // Initialize API
    const apiConfig = this.config.getDerivAPIConfig();
    this.api = new DerivAPIService(apiConfig);
    
    // Initialize strategy
    this.strategy = this.createStrategy();
    
    this.setupEventListeners();
  }

  /**
   * Start the bot
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Bot is already running');
    }

    try {
      this.logger.info('Starting Deriv Bot...');
      
      // Connect to API
      await this.api.connect();
      this.logger.info('Connected to Deriv API');
      
      // Authorize if token is provided
      if (this.config.getDerivAPIConfig().apiToken) {
        await this.api.authorize();
        this.logger.info('Authorized with Deriv API');
        
        // Get initial balance
        await this.updateBalance();
      }
      
      // Start trading if enabled
      if (this.config.isTradingEnabled()) {
        await this.startTrading();
      } else {
        this.logger.info('Trading is disabled. Bot will only monitor market data.');
        await this.startMonitoring();
      }
      
      this.isRunning = true;
      this.stats.isActive = true;
      this.emit('started');
      
    } catch (error) {
      this.logger.error('Failed to start bot', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Stop the bot
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('Stopping Deriv Bot...');
    
    this.isRunning = false;
    this.stats.isActive = false;
    
    // Disconnect from API
    this.api.disconnect();
    
    // Log final statistics
    this.logStatistics();
    
    this.emit('stopped');
    this.logger.info('Bot stopped');
  }

  /**
   * Start trading mode
   */
  private async startTrading(): Promise<void> {
    const tradingConfig = this.config.getTradingConfig();
    
    this.logger.info('Starting trading mode', { symbol: tradingConfig.symbol });
    
    // Subscribe to tick data
    await this.api.subscribeTicks(tradingConfig.symbol);
    
    this.logger.info(`Subscribed to ${tradingConfig.symbol} ticks`);
  }

  /**
   * Start monitoring mode (no trading)
   */
  private async startMonitoring(): Promise<void> {
    const tradingConfig = this.config.getTradingConfig();
    
    this.logger.info('Starting monitoring mode', { symbol: tradingConfig.symbol });
    
    // Subscribe to tick data
    await this.api.subscribeTicks(tradingConfig.symbol);
    
    this.logger.info(`Monitoring ${tradingConfig.symbol} ticks`);
  }

  /**
   * Handle incoming tick data
   */
  private async handleTick(tickData: TickData): Promise<void> {
    const stopTimer = this.monitor.startTimer('handleTick');

    try {


      // Add tick to strategy
      this.strategy.addTick(tickData);

      // Generate signal (always, regardless of trading mode)
      const signal = this.strategy.generateSignal();

      // Debug logging for tests
      const tickCount = (this.strategy as any).tickHistory?.length || 0;
      this.logger.debug('Tick processed', {
        tickCount,
        hasSignal: !!signal,
        price: tickData.tick.quote
      });

      if (signal) {
        // Emit signal event for testing/monitoring
        this.emit('signal_generated', signal);

        // Execute trade only if trading is enabled and conditions are met
        if (this.config.isTradingEnabled() && this.canTrade() && this.shouldExecuteSignal(signal)) {
          await this.executeTrade(signal);
        }
      }
      
      // Reset daily stats if new day
      this.checkDailyReset();
      
    } catch (error) {
      this.logger.error('Error handling tick', error instanceof Error ? error : new Error(String(error)));
    } finally {
      stopTimer();
    }
  }

  /**
   * Execute a trade based on signal
   */
  private async executeTrade(signal: TradingSignal): Promise<void> {
    const stopTimer = this.monitor.startTimer('executeTrade');
    
    try {
      const tradingConfig = this.config.getTradingConfig();
      
      this.logger.trade('Executing trade', {
        signal,
        stake: tradingConfig.stake,
        symbol: tradingConfig.symbol,
      });
      
      // Create proposal request
      const proposalRequest: ProposalRequest = {
        proposal: 1,
        amount: tradingConfig.stake,
        basis: 'stake',
        contract_type: signal.direction === 'CALL' ? 'CALL' : 'PUT',
        currency: tradingConfig.currency,
        duration: tradingConfig.duration,
        duration_unit: tradingConfig.durationUnit,
        symbol: tradingConfig.symbol,
      };
      
      // Get price proposal
      const proposal = await this.api.getPriceProposal(proposalRequest);
      
      if (!proposal.proposal) {
        throw new Error('No proposal received');
      }
      
      // Buy the contract
      const buyRequest: BuyRequest = {
        buy: proposal.proposal.id,
        price: proposal.proposal.ask_price,
      };
      
      const buyResponse = await this.api.buyContract(buyRequest);
      
      if (buyResponse.buy) {
        const trade: TradeResult = {
          id: buyResponse.buy.contract_id.toString(),
          symbol: tradingConfig.symbol,
          contractType: signal.direction,
          stake: buyResponse.buy.buy_price,
          payout: buyResponse.buy.payout,
          profit: 0, // Will be updated when contract settles
          timestamp: Date.now(),
          status: 'pending',
        };
        
        this.tradeHistory.push(trade);
        this.stats.totalTrades++;
        this.stats.lastTradeTime = Date.now();
        
        // Subscribe to contract updates
        await this.api.getContract(buyResponse.buy.contract_id);
        
        this.logger.trade('Trade executed successfully', {
          contractId: buyResponse.buy.contract_id,
          stake: buyResponse.buy.buy_price,
          payout: buyResponse.buy.payout,
        });
        
        this.emit('trade_executed', trade);
      }
      
    } catch (error) {
      this.logger.error('Failed to execute trade', error instanceof Error ? error : new Error(String(error)));
      this.emit('trade_error', error);
    } finally {
      stopTimer();
    }
  }

  /**
   * Check if bot can trade
   */
  private canTrade(): boolean {
    const riskConfig = this.config.getRiskConfig();
    
    // Check daily loss limit
    if (this.dailyProfit <= -riskConfig.maxDailyLoss) {
      this.logger.risk('Daily loss limit reached', {
        dailyProfit: this.dailyProfit,
        maxDailyLoss: riskConfig.maxDailyLoss,
      });
      this.emit('trading_stopped', { reason: 'daily_loss_limit', dailyProfit: this.dailyProfit });
      return false;
    }

    // Check consecutive losses
    if (this.stats.consecutiveLosses >= riskConfig.maxConsecutiveLosses) {
      this.logger.risk('Consecutive losses limit reached', {
        consecutiveLosses: this.stats.consecutiveLosses,
        maxConsecutiveLosses: riskConfig.maxConsecutiveLosses,
      });
      this.emit('trading_stopped', { reason: 'consecutive_losses', consecutiveLosses: this.stats.consecutiveLosses });
      return false;
    }
    
    return true;
  }

  /**
   * Check if signal should be executed
   */
  private shouldExecuteSignal(signal: TradingSignal): boolean {
    // Check signal confidence
    const strategyConfig = this.config.getBotConfig().strategy;
    const threshold = (strategyConfig.parameters['signalThreshold'] as number) || 0.6;
    
    if (signal.confidence < threshold) {
      return false;
    }
    
    // Check time since last trade (prevent overtrading)
    const minTimeBetweenTrades = 60000; // 1 minute
    if (Date.now() - this.stats.lastTradeTime < minTimeBetweenTrades) {
      return false;
    }
    
    return true;
  }

  /**
   * Create trading strategy based on configuration
   */
  private createStrategy(): TradingStrategy {
    const strategyConfig = this.config.getBotConfig().strategy;
    
    switch (strategyConfig.type) {
      case 'simple_trend':
        return new SimpleTrendStrategy(
          (strategyConfig.parameters['signalThreshold'] as number) || 0.6,
          (strategyConfig.parameters['lookbackPeriods'] as number) || 10
        );
      case 'ma_crossover':
        return new MovingAverageCrossoverStrategy(5, 15, 0.7);
      default:
        this.logger.warn(`Unknown strategy type: ${strategyConfig.type}, using simple_trend`);
        return new SimpleTrendStrategy();
    }
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    this.api.on('tick', (tickData: TickData) => {
      this.handleTick(tickData).catch((error) => {
        this.logger.error('Error in tick handler', error);
      });
    });
    
    this.api.on('balance', (balanceData: unknown) => {
      this.handleBalanceUpdate(balanceData);
    });
    
    this.api.on('contract_update', (contractData: unknown) => {
      this.handleContractUpdate(contractData);
    });
    
    this.api.on('error', (error: Error) => {
      this.logger.error('API error', error);
      this.emit('error', error);
    });
    
    this.api.on('disconnected', () => {
      this.logger.warn('API disconnected');
      this.emit('disconnected');
    });
  }

  /**
   * Handle balance updates
   */
  private handleBalanceUpdate(balanceData: unknown): void {
    // Implementation depends on balance data structure
    this.logger.debug('Balance updated', balanceData as Record<string, unknown>);
  }

  /**
   * Handle contract updates
   */
  private handleContractUpdate(contractData: unknown): void {
    // Implementation depends on contract data structure
    this.logger.debug('Contract updated', contractData as Record<string, unknown>);
  }

  /**
   * Update current balance
   */
  private async updateBalance(): Promise<void> {
    try {
      const balanceResponse = await this.api.getBalance();
      // Implementation depends on balance response structure
      this.logger.debug('Balance retrieved', balanceResponse as Record<string, unknown>);
    } catch (error) {
      this.logger.error('Failed to get balance', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Check if daily reset is needed
   */
  private checkDailyReset(): void {
    const currentDate = new Date().toDateString();
    if (currentDate !== this.lastResetDate) {
      this.dailyProfit = 0;
      this.lastResetDate = currentDate;
      this.logger.info('Daily stats reset');
    }
  }

  /**
   * Log current statistics
   */
  private logStatistics(): void {
    this.logger.info('Bot Statistics', {
      stats: this.stats,
      dailyProfit: this.dailyProfit,
      totalTrades: this.tradeHistory.length,
    });
  }

  /**
   * Get current bot statistics
   */
  public getStats(): BotStats {
    return { ...this.stats };
  }

  /**
   * Get trade history
   */
  public getTradeHistory(): TradeResult[] {
    return [...this.tradeHistory];
  }
}
