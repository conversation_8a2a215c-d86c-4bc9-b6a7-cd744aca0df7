import * as dotenv from 'dotenv';
import { BotConfig, TradeConfig, RiskManagement } from '../types';

// Load environment variables
dotenv.config();

export class ConfigManager {
  private static instance: ConfigManager;
  private config: BotConfig;

  private constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): BotConfig {
    return {
      trading: {
        symbol: this.getEnvString('DEFAULT_SYMBOL', 'R_100'),
        stake: this.getEnvNumber('DEFAULT_STAKE', 10),
        currency: this.getEnvString('DEFAULT_CURRENCY', 'USD'),
        duration: this.getEnvNumber('DEFAULT_DURATION', 5),
        durationUnit: this.getEnvString('DEFAULT_DURATION_UNIT', 'min') as 'min' | 'h' | 'd' | 't' | 's',
        contractType: 'CALL',
        allowEquals: false,
      },
      risk: {
        maxDailyLoss: this.getEnvNumber('MAX_DAILY_LOSS', 100),
        maxConsecutiveLosses: this.getEnvNumber('MAX_CONSECUTIVE_LOSSES', 3),
        stopLossPercentage: this.getEnvNumber('STOP_LOSS_PERCENTAGE', 50),
        maxStakePercentage: this.getEnvNumber('MAX_STAKE_PERCENTAGE', 10),
      },
      strategy: {
        type: this.getEnvString('STRATEGY_TYPE', 'simple_trend'),
        parameters: {
          signalThreshold: this.getEnvNumber('SIGNAL_THRESHOLD', 0.6),
          lookbackPeriods: this.getEnvNumber('LOOKBACK_PERIODS', 10),
        },
      },
    };
  }

  /**
   * Validate configuration values
   */
  private validateConfiguration(): void {
    const errors: string[] = [];

    // Validate trading config
    if (this.config.trading.stake <= 0) {
      errors.push('Stake must be greater than 0');
    }

    if (this.config.trading.duration <= 0) {
      errors.push('Duration must be greater than 0');
    }

    if (!['min', 'h', 'd', 't', 's'].includes(this.config.trading.durationUnit)) {
      errors.push('Invalid duration unit');
    }

    // Validate risk management
    if (this.config.risk.maxDailyLoss <= 0) {
      errors.push('Max daily loss must be greater than 0');
    }

    if (this.config.risk.maxConsecutiveLosses <= 0) {
      errors.push('Max consecutive losses must be greater than 0');
    }

    if (this.config.risk.stopLossPercentage <= 0 || this.config.risk.stopLossPercentage > 100) {
      errors.push('Stop loss percentage must be between 0 and 100');
    }

    if (this.config.risk.maxStakePercentage <= 0 || this.config.risk.maxStakePercentage > 100) {
      errors.push('Max stake percentage must be between 0 and 100');
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Get Deriv API configuration
   */
  public getDerivAPIConfig(): {
    appId: string;
    apiToken?: string;
    apiUrl?: string;
  } {
    const appId = this.getEnvString('DERIV_APP_ID');
    if (!appId) {
      throw new Error('DERIV_APP_ID is required');
    }

    return {
      appId,
      apiToken: this.getEnvString('DERIV_API_TOKEN'),
      apiUrl: this.getEnvString('DERIV_API_URL'),
    };
  }

  /**
   * Get bot configuration
   */
  public getBotConfig(): BotConfig {
    return { ...this.config };
  }

  /**
   * Get trading configuration
   */
  public getTradingConfig(): TradeConfig {
    return { ...this.config.trading };
  }

  /**
   * Get risk management configuration
   */
  public getRiskConfig(): RiskManagement {
    return { ...this.config.risk };
  }

  /**
   * Update trading configuration
   */
  public updateTradingConfig(updates: Partial<TradeConfig>): void {
    this.config.trading = { ...this.config.trading, ...updates };
    this.validateConfiguration();
  }

  /**
   * Update risk management configuration
   */
  public updateRiskConfig(updates: Partial<RiskManagement>): void {
    this.config.risk = { ...this.config.risk, ...updates };
    this.validateConfiguration();
  }

  /**
   * Check if trading is enabled
   */
  public isTradingEnabled(): boolean {
    return this.getEnvBoolean('TRADING_ENABLED', false);
  }

  /**
   * Get environment type
   */
  public getEnvironment(): string {
    return this.getEnvString('ENVIRONMENT', 'demo');
  }

  /**
   * Get log level
   */
  public getLogLevel(): string {
    return this.getEnvString('LOG_LEVEL', 'info');
  }

  /**
   * Helper method to get string environment variable
   */
  private getEnvString(key: string, defaultValue?: string): string {
    const value = process.env[key];
    if (value === undefined) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`Environment variable ${key} is required`);
    }
    return value;
  }

  /**
   * Helper method to get number environment variable
   */
  private getEnvNumber(key: string, defaultValue?: number): number {
    const value = process.env[key];
    if (value === undefined) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`Environment variable ${key} is required`);
    }
    const parsed = parseFloat(value);
    if (isNaN(parsed)) {
      throw new Error(`Environment variable ${key} must be a valid number`);
    }
    return parsed;
  }

  /**
   * Helper method to get boolean environment variable
   */
  private getEnvBoolean(key: string, defaultValue?: boolean): boolean {
    const value = process.env[key];
    if (value === undefined) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`Environment variable ${key} is required`);
    }
    return value.toLowerCase() === 'true';
  }

  /**
   * Export configuration to JSON
   */
  public exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from JSON
   */
  public importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson) as BotConfig;
      this.config = importedConfig;
      this.validateConfiguration();
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
