#!/usr/bin/env node

import { DerivBot } from './bot/DerivBot';
import { Logger } from './utils/Logger';
import { ConfigManager } from './config/ConfigManager';

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  const logger = Logger.getInstance();
  const config = ConfigManager.getInstance();

  logger.info('='.repeat(50));
  logger.info('🤖 Deriv Rise/Fall Trading Bot Starting...');
  logger.info('='.repeat(50));

  try {
    // Validate configuration
    const derivConfig = config.getDerivAPIConfig();
    if (!derivConfig.appId) {
      throw new Error('DERIV_APP_ID is required. Please check your .env file.');
    }

    logger.info('Configuration loaded successfully', {
      environment: config.getEnvironment(),
      tradingEnabled: config.isTradingEnabled(),
      symbol: config.getTradingConfig().symbol,
      strategy: config.getBotConfig().strategy.type,
    });

    // Create and configure bot
    const bot = new DerivBot();

    // Setup bot event listeners
    bot.on('started', () => {
      logger.info('✅ Bot started successfully');
    });

    bot.on('stopped', () => {
      logger.info('🛑 Bot stopped');
    });

    bot.on('trade_executed', (trade) => {
      logger.trade('Trade executed', trade);
    });

    bot.on('trade_error', (error) => {
      logger.error('Trade execution failed', error);
    });

    bot.on('error', (error) => {
      logger.error('Bot error', error);
    });

    bot.on('disconnected', () => {
      logger.warn('Bot disconnected from API');
    });

    // Handle graceful shutdown
    const shutdown = async (signal: string): Promise<void> => {
      logger.info(`Received ${signal}. Shutting down gracefully...`);
      try {
        await bot.stop();
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', error instanceof Error ? error : new Error(String(error)));
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      shutdown('uncaughtException').catch(() => {
        process.exit(1);
      });
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      shutdown('unhandledRejection').catch(() => {
        process.exit(1);
      });
    });

    // Start the bot
    await bot.start();

    // Keep the process running
    logger.info('Bot is running. Press Ctrl+C to stop.');

    // Optional: Log statistics periodically
    setInterval(() => {
      const stats = bot.getStats();
      if (stats.totalTrades > 0) {
        logger.info('Periodic stats update', {
          totalTrades: stats.totalTrades,
          winRate: `${(stats.winRate * 100).toFixed(2)}%`,
          totalProfit: stats.totalProfit,
          consecutiveLosses: stats.consecutiveLosses,
          isActive: stats.isActive,
        });
      }
    }, 300000); // Every 5 minutes

  } catch (error) {
    logger.error('Failed to start bot', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

// Run the application
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { DerivBot } from './bot/DerivBot';
export { DerivAPIService } from './services/DerivAPIService';
export { ConfigManager } from './config/ConfigManager';
export { Logger } from './utils/Logger';
export * from './types';
export * from './strategies/TradingStrategy';
