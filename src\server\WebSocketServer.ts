import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { EventEmitter } from 'events';

export interface WebSocketServerConfig {
  port: number;
  corsOrigin: string;
}

export class WebSocketServer extends EventEmitter {
  private app: express.Application;
  private server: any;
  private io: Server;
  private port: number;
  private isRunning: boolean = false;

  constructor(config: WebSocketServerConfig) {
    super();
    this.port = config.port;
    
    // Setup Express app
    this.app = express();
    this.app.use(cors({
      origin: config.corsOrigin,
      credentials: true
    }));
    this.app.use(express.json());

    // Create HTTP server
    this.server = createServer(this.app);

    // Setup Socket.IO
    this.io = new Server(this.server, {
      cors: {
        origin: config.corsOrigin,
        methods: ['GET', 'POST'],
        credentials: true
      }
    });

    this.setupRoutes();
    this.setupSocketHandlers();
  }

  private setupRoutes(): void {
    this.app.get('/health', (_req: any, res: any) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        connections: this.io.engine.clientsCount
      });
    });

    this.app.get('/status', (_req: any, res: any) => {
      res.json({
        isRunning: this.isRunning,
        connections: this.io.engine.clientsCount,
        uptime: process.uptime()
      });
    });
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: any) => {
      console.log(`Client connected: ${socket.id}`);
      
      // Send current status to new client
      this.emit('client_connected', socket.id);

      // Handle bot control commands
      socket.on('start_bot', () => {
        console.log(`Start bot command from ${socket.id}`);
        this.emit('start_bot_command', socket.id);
      });

      socket.on('stop_bot', () => {
        console.log(`Stop bot command from ${socket.id}`);
        this.emit('stop_bot_command', socket.id);
      });

      socket.on('get_status', () => {
        this.emit('status_request', socket.id);
      });

      socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
        this.emit('client_disconnected', socket.id);
      });
    });
  }

  // Methods to broadcast data to all connected clients
  public broadcastBotStatus(status: any): void {
    this.io.emit('bot_status', status);
  }

  public broadcastTickData(tickData: any): void {
    this.io.emit('tick_data', tickData);
  }

  public broadcastTradeSignal(signal: any): void {
    this.io.emit('trade_signal', signal);
  }

  public broadcastTradeResult(result: any): void {
    this.io.emit('trade_result', result);
  }

  public broadcastLog(log: any): void {
    this.io.emit('log', log);
  }

  public broadcastStats(stats: any): void {
    this.io.emit('stats', stats);
  }

  // Send data to specific client
  public sendToClient(clientId: string, event: string, data: any): void {
    this.io.to(clientId).emit(event, data);
  }

  public start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(this.port, () => {
          this.isRunning = true;
          console.log(`WebSocket server running on port ${this.port}`);
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  public stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.isRunning = false;
          console.log('WebSocket server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  public getConnectionCount(): number {
    return this.io.engine.clientsCount;
  }

  public isServerRunning(): boolean {
    return this.isRunning;
  }
}
