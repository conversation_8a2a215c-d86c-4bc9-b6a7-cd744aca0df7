import WebSocket from 'ws';
import { EventEmitter } from 'events';
import {
  DerivAPIConfig,
  TickData,
  ActiveSymbol,
  ProposalRequest,
  ProposalResponse,
  BuyRequest,
  BuyResponse,
  DerivAPIError,
  WSMessage,
} from '../types';

export class DerivAPIService extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: DerivAPIConfig;
  private requestId = 1;
  private pendingRequests = new Map<number, {
    resolve: (value: unknown) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(config: DerivAPIConfig) {
    super();
    this.config = {
      apiUrl: 'wss://ws.derivws.com/websockets/v3',
      language: 'en',
      ...config,
    };
  }

  /**
   * Connect to Deriv WebSocket API
   */
  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const url = `${this.config.apiUrl}?app_id=${this.config.appId}&l=${this.config.language}`;
        this.ws = new WebSocket(url);

        this.ws.on('open', () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.emit('connected');
          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          this.handleMessage(data.toString());
        });

        this.ws.on('close', () => {
          this.isConnected = false;
          this.emit('disconnected');
          this.handleReconnect();
        });

        this.ws.on('error', (error: Error) => {
          this.emit('error', error);
          if (!this.isConnected) {
            reject(error);
          }
        });

        // Connection timeout
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Connection timeout'));
          }
        }, 10000);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.clearPendingRequests();
  }

  /**
   * Authenticate with API token
   */
  public async authorize(): Promise<unknown> {
    if (!this.config.apiToken) {
      throw new Error('API token is required for authorization');
    }

    return this.sendRequest({
      authorize: this.config.apiToken,
    });
  }

  /**
   * Get active symbols
   */
  public async getActiveSymbols(): Promise<ActiveSymbol[]> {
    const response = await this.sendRequest({
      active_symbols: 'brief',
      product_type: 'basic',
    }) as { active_symbols: ActiveSymbol[] };

    return response.active_symbols;
  }

  /**
   * Get contracts for a symbol
   */
  public async getContractsForSymbol(symbol: string): Promise<unknown> {
    return this.sendRequest({
      contracts_for: symbol,
      product_type: 'basic',
    });
  }

  /**
   * Subscribe to tick data for a symbol
   */
  public async subscribeTicks(symbol: string): Promise<void> {
    await this.sendRequest({
      ticks: symbol,
      subscribe: 1,
    });
  }

  /**
   * Unsubscribe from tick data
   */
  public async unsubscribeTicks(symbol: string): Promise<void> {
    await this.sendRequest({
      forget: symbol,
    });
  }

  /**
   * Get price proposal
   */
  public async getPriceProposal(request: ProposalRequest): Promise<ProposalResponse> {
    return this.sendRequest(request as unknown as Record<string, unknown>) as Promise<ProposalResponse>;
  }

  /**
   * Buy a contract
   */
  public async buyContract(request: BuyRequest): Promise<BuyResponse> {
    return this.sendRequest(request as unknown as Record<string, unknown>) as Promise<BuyResponse>;
  }

  /**
   * Get account balance
   */
  public async getBalance(): Promise<unknown> {
    return this.sendRequest({
      balance: 1,
      subscribe: 1,
    });
  }

  /**
   * Get portfolio
   */
  public async getPortfolio(): Promise<unknown> {
    return this.sendRequest({
      portfolio: 1,
    });
  }

  /**
   * Get contract details
   */
  public async getContract(contractId: number): Promise<unknown> {
    return this.sendRequest({
      proposal_open_contract: 1,
      contract_id: contractId,
      subscribe: 1,
    });
  }

  /**
   * Send a request to the API
   */
  private async sendRequest(request: Record<string, unknown>): Promise<unknown> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket is not connected');
    }

    const reqId = this.requestId++;
    const message = {
      ...request,
      req_id: reqId,
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(reqId);
        reject(new Error('Request timeout'));
      }, 30000);

      this.pendingRequests.set(reqId, {
        resolve,
        reject,
        timeout,
      });

      this.ws!.send(JSON.stringify(message));
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(data: string): void {
    try {
      const message: WSMessage = JSON.parse(data) as WSMessage;

      // Handle subscription messages (ticks, balance, etc.)
      if (message.msg_type === 'tick') {
        this.emit('tick', message as unknown as TickData);
        // Also resolve pending subscription request if this is the first tick
        if (message.req_id && this.pendingRequests.has(message.req_id)) {
          const pending = this.pendingRequests.get(message.req_id)!;
          this.pendingRequests.delete(message.req_id);
          clearTimeout(pending.timeout);
          pending.resolve(message);
        }
        return;
      }

      if (message.msg_type === 'balance') {
        this.emit('balance', message);
        // Also resolve pending subscription request if this is the first balance
        if (message.req_id && this.pendingRequests.has(message.req_id)) {
          const pending = this.pendingRequests.get(message.req_id)!;
          this.pendingRequests.delete(message.req_id);
          clearTimeout(pending.timeout);
          pending.resolve(message);
        }
        return;
      }

      if (message.msg_type === 'proposal_open_contract') {
        this.emit('contract_update', message);
        return;
      }

      // Handle request responses
      if (message.req_id && this.pendingRequests.has(message.req_id)) {
        const pending = this.pendingRequests.get(message.req_id)!;
        this.pendingRequests.delete(message.req_id);
        clearTimeout(pending.timeout);

        if ('error' in message) {
          const error = message as unknown as DerivAPIError;
          pending.reject(new Error(`API Error: ${error.error.message} (${error.error.code})`));
        } else {
          pending.resolve(message);
        }
      }
    } catch (error) {
      this.emit('error', new Error(`Failed to parse message: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        this.connect().catch((error) => {
          this.emit('error', error);
        });
      }, delay);
    } else {
      this.emit('error', new Error('Max reconnection attempts reached'));
    }
  }

  /**
   * Clear all pending requests
   */
  private clearPendingRequests(): void {
    for (const [, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Connection closed'));
    }
    this.pendingRequests.clear();
  }

  /**
   * Check if connected
   */
  public isConnectedToAPI(): boolean {
    return this.isConnected;
  }
}
