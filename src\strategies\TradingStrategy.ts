import { TradingSignal, TickData } from '../types';
import { Logger } from '../utils/Logger';

export abstract class TradingStrategy {
  protected logger: Logger;
  protected tickHistory: TickData[] = [];
  protected maxHistorySize: number = 100;

  constructor() {
    this.logger = Logger.getInstance();
  }

  /**
   * Abstract method to generate trading signal
   */
  public abstract generateSignal(): TradingSignal | null;

  /**
   * Add new tick data to history
   */
  public addTick(tick: TickData): void {
    this.tickHistory.push(tick);
    
    // Keep only the last maxHistorySize ticks
    if (this.tickHistory.length > this.maxHistorySize) {
      this.tickHistory = this.tickHistory.slice(-this.maxHistorySize);
    }

    this.logger.debug('Tick added to strategy', {
      symbol: tick.tick.symbol,
      quote: tick.tick.quote,
      historySize: this.tickHistory.length,
    });
  }

  /**
   * Get the latest tick
   */
  protected getLatestTick(): TickData | null {
    return this.tickHistory.length > 0 ? this.tickHistory[this.tickHistory.length - 1]! : null;
  }

  /**
   * Get tick history
   */
  protected getTickHistory(count?: number): TickData[] {
    if (count) {
      return this.tickHistory.slice(-count);
    }
    return [...this.tickHistory];
  }

  /**
   * Calculate simple moving average
   */
  protected calculateSMA(period: number): number | null {
    if (this.tickHistory.length < period) {
      return null;
    }

    const recentTicks = this.tickHistory.slice(-period);
    const sum = recentTicks.reduce((acc, tick) => acc + tick.tick.quote, 0);
    return sum / period;
  }

  /**
   * Calculate exponential moving average
   */
  protected calculateEMA(period: number): number | null {
    if (this.tickHistory.length < period) {
      return null;
    }

    const multiplier = 2 / (period + 1);
    let ema = this.tickHistory[0]!.tick.quote;

    for (let i = 1; i < this.tickHistory.length; i++) {
      ema = (this.tickHistory[i]!.tick.quote * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  /**
   * Calculate price change percentage
   */
  protected calculatePriceChange(periods: number = 1): number | null {
    if (this.tickHistory.length < periods + 1) {
      return null;
    }

    const currentPrice = this.tickHistory[this.tickHistory.length - 1]!.tick.quote;
    const previousPrice = this.tickHistory[this.tickHistory.length - 1 - periods]!.tick.quote;
    
    return ((currentPrice - previousPrice) / previousPrice) * 100;
  }

  /**
   * Calculate volatility (standard deviation)
   */
  protected calculateVolatility(period: number): number | null {
    if (this.tickHistory.length < period) {
      return null;
    }

    const recentTicks = this.tickHistory.slice(-period);
    const prices = recentTicks.map(tick => tick.tick.quote);
    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    
    const squaredDifferences = prices.map(price => Math.pow(price - mean, 2));
    const variance = squaredDifferences.reduce((sum, diff) => sum + diff, 0) / prices.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Detect trend direction
   */
  protected detectTrend(period: number = 10): 'up' | 'down' | 'sideways' | null {
    if (this.tickHistory.length < period) {
      return null;
    }

    const recentTicks = this.tickHistory.slice(-period);
    const firstPrice = recentTicks[0]!.tick.quote;
    const lastPrice = recentTicks[recentTicks.length - 1]!.tick.quote;
    
    const priceChange = ((lastPrice - firstPrice) / firstPrice) * 100;
    
    if (priceChange > 0.1) {
      return 'up';
    } else if (priceChange < -0.1) {
      return 'down';
    } else {
      return 'sideways';
    }
  }

  /**
   * Reset strategy state
   */
  public reset(): void {
    this.tickHistory = [];
    this.logger.info('Strategy reset');
  }
}

/**
 * Simple trend-following strategy
 */
export class SimpleTrendStrategy extends TradingStrategy {
  private signalThreshold: number;
  private lookbackPeriods: number;

  constructor(signalThreshold: number = 0.6, lookbackPeriods: number = 10) {
    super();
    this.signalThreshold = signalThreshold;
    this.lookbackPeriods = lookbackPeriods;
    this.maxHistorySize = Math.max(50, lookbackPeriods * 2);
  }

  public generateSignal(): TradingSignal | null {
    if (this.tickHistory.length < this.lookbackPeriods) {
      return null;
    }

    const trend = this.detectTrend(this.lookbackPeriods);
    const priceChange = this.calculatePriceChange(this.lookbackPeriods);
    const volatility = this.calculateVolatility(this.lookbackPeriods);

    if (!trend || priceChange === null || volatility === null) {
      return null;
    }

    let confidence = 0;
    let direction: 'CALL' | 'PUT' = 'CALL';
    let reason = '';

    // Determine signal based on trend and momentum
    if (trend === 'up' && priceChange > 0.05) {
      direction = 'CALL';
      confidence = Math.min(0.9, Math.abs(priceChange) / 2 + 0.5);
      reason = `Upward trend detected with ${priceChange.toFixed(2)}% price increase`;
    } else if (trend === 'down' && priceChange < -0.05) {
      direction = 'PUT';
      confidence = Math.min(0.9, Math.abs(priceChange) / 2 + 0.5);
      reason = `Downward trend detected with ${priceChange.toFixed(2)}% price decrease`;
    } else {
      // No clear signal
      return null;
    }

    // Adjust confidence based on volatility
    if (volatility > 0.5) {
      confidence *= 0.8; // Reduce confidence in high volatility
      reason += ` (high volatility: ${volatility.toFixed(4)})`;
    }

    // Only return signal if confidence meets threshold
    if (confidence >= this.signalThreshold) {
      const signal: TradingSignal = {
        direction,
        confidence,
        timestamp: Date.now(),
        reason,
      };

      this.logger.strategy('Signal generated', {
        signal,
        trend,
        priceChange,
        volatility,
      });

      return signal;
    }

    return null;
  }
}

/**
 * Moving Average Crossover Strategy
 */
export class MovingAverageCrossoverStrategy extends TradingStrategy {
  private shortPeriod: number;
  private longPeriod: number;
  private signalThreshold: number;

  constructor(shortPeriod: number = 5, longPeriod: number = 15, signalThreshold: number = 0.7) {
    super();
    this.shortPeriod = shortPeriod;
    this.longPeriod = longPeriod;
    this.signalThreshold = signalThreshold;
    this.maxHistorySize = Math.max(50, longPeriod * 2);
  }

  public generateSignal(): TradingSignal | null {
    if (this.tickHistory.length < this.longPeriod) {
      return null;
    }

    const shortMA = this.calculateSMA(this.shortPeriod);
    const longMA = this.calculateSMA(this.longPeriod);

    if (shortMA === null || longMA === null) {
      return null;
    }

    // Get previous MAs to detect crossover
    const prevShortMA = this.calculateSMAAtIndex(this.shortPeriod, 1);
    const prevLongMA = this.calculateSMAAtIndex(this.longPeriod, 1);

    if (prevShortMA === null || prevLongMA === null) {
      return null;
    }

    let signal: TradingSignal | null = null;

    // Bullish crossover: short MA crosses above long MA
    if (prevShortMA <= prevLongMA && shortMA > longMA) {
      const crossoverStrength = (shortMA - longMA) / longMA;
      const confidence = Math.min(0.9, this.signalThreshold + crossoverStrength * 10);

      signal = {
        direction: 'CALL',
        confidence,
        timestamp: Date.now(),
        reason: `Bullish MA crossover: Short MA (${shortMA.toFixed(4)}) > Long MA (${longMA.toFixed(4)})`,
      };
    }
    // Bearish crossover: short MA crosses below long MA
    else if (prevShortMA >= prevLongMA && shortMA < longMA) {
      const crossoverStrength = (longMA - shortMA) / longMA;
      const confidence = Math.min(0.9, this.signalThreshold + crossoverStrength * 10);

      signal = {
        direction: 'PUT',
        confidence,
        timestamp: Date.now(),
        reason: `Bearish MA crossover: Short MA (${shortMA.toFixed(4)}) < Long MA (${longMA.toFixed(4)})`,
      };
    }

    if (signal) {
      this.logger.strategy('MA Crossover signal generated', {
        signal,
        shortMA,
        longMA,
        prevShortMA,
        prevLongMA,
      });
    }

    return signal;
  }

  private calculateSMAAtIndex(period: number, indexFromEnd: number): number | null {
    if (this.tickHistory.length < period + indexFromEnd) {
      return null;
    }

    const endIndex = this.tickHistory.length - indexFromEnd;
    const startIndex = endIndex - period;
    const relevantTicks = this.tickHistory.slice(startIndex, endIndex);
    
    const sum = relevantTicks.reduce((acc, tick) => acc + tick.tick.quote, 0);
    return sum / period;
  }
}
