import { EventEmitter } from 'events';
import { TickData, ProposalRequest, ProposalResponse, BuyRequest, BuyResponse } from '../types';

/**
 * Mock implementation of DerivAPIService for testing
 */
export class MockDerivAPIService extends EventEmitter {
  private connected = false;
  private authenticated = false;
  private tickInterval: NodeJS.Timeout | null = null;
  private currentPrice = 100;

  constructor() {
    super();
  }

  /**
   * Mock connect method
   */
  async connect(): Promise<void> {
    this.connected = true;
    this.emit('connected');
    return Promise.resolve();
  }

  /**
   * Mock disconnect method
   */
  async disconnect(): Promise<void> {
    this.connected = false;
    if (this.tickInterval) {
      clearInterval(this.tickInterval);
      this.tickInterval = null;
    }
    this.emit('disconnected');
    return Promise.resolve();
  }

  /**
   * Mock authenticate method
   */
  async authenticate(token: string): Promise<void> {
    if (!token) {
      throw new Error('Invalid token');
    }
    this.authenticated = true;
    this.emit('authenticated');
    return Promise.resolve();
  }

  /**
   * <PERSON><PERSON> authorize method
   */
  async authorize(): Promise<void> {
    this.authenticated = true;
    this.emit('authorized');
    return Promise.resolve();
  }

  /**
   * Mock subscribe to ticks
   */
  async subscribeToTicks(symbol: string): Promise<void> {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    // Start generating mock tick data
    this.tickInterval = setInterval(() => {
      this.generateMockTick(symbol);
    }, 1000);

    return Promise.resolve();
  }

  /**
   * Mock subscribeTicks method (alias for subscribeToTicks)
   */
  async subscribeTicks(symbol: string): Promise<void> {
    return this.subscribeToTicks(symbol);
  }

  /**
   * Mock unsubscribe from ticks
   */
  async unsubscribeFromTicks(_symbol: string): Promise<void> {
    if (this.tickInterval) {
      clearInterval(this.tickInterval);
      this.tickInterval = null;
    }
    return Promise.resolve();
  }

  /**
   * Mock get price proposal
   */
  async getPriceProposal(request: ProposalRequest): Promise<ProposalResponse> {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    // Mock proposal response
    const mockResponse: ProposalResponse = {
      proposal: {
        id: `mock_proposal_${Date.now()}`,
        ask_price: 10.5,
        date_start: Math.floor(Date.now() / 1000),
        display_value: '10.50',
        longcode: `Mock ${request.contract_type} contract`,
        payout: request.amount * 1.95,
        spot: this.currentPrice,
        spot_time: Math.floor(Date.now() / 1000),
      },
      echo_req: request,
    };

    return Promise.resolve(mockResponse);
  }

  /**
   * Mock buy contract
   */
  async buyContract(request: BuyRequest): Promise<BuyResponse> {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    if (!this.authenticated) {
      throw new Error('Not authenticated');
    }

    // Mock buy response
    const mockResponse: BuyResponse = {
      buy: {
        balance_after: 1000 - request.price,
        buy_price: request.price,
        contract_id: Math.floor(Math.random() * 1000000),
        longcode: `Mock contract`,
        payout: request.price * 1.95,
        purchase_time: Math.floor(Date.now() / 1000),
        shortcode: `MOCK_CONTRACT`,
        start_time: Math.floor(Date.now() / 1000),
        transaction_id: Math.floor(Math.random() * 1000000),
      },
    };

    return Promise.resolve(mockResponse);
  }

  /**
   * Mock get balance
   */
  async getBalance(): Promise<any> {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    return Promise.resolve({
      balance: {
        balance: 1000,
        currency: 'USD',
        loginid: 'MOCK123',
      },
      echo_req: { balance: 1 },
      msg_type: 'balance',
    });
  }

  /**
   * Generate mock tick data
   */
  private generateMockTick(symbol: string): void {
    // Simulate price movement
    const change = (Math.random() - 0.5) * 2; // Random change between -1 and 1
    this.currentPrice += change;

    const mockTick: TickData = {
      tick: {
        ask: this.currentPrice + 0.001,
        bid: this.currentPrice - 0.001,
        epoch: Math.floor(Date.now() / 1000),
        id: `mock_tick_${Date.now()}`,
        pip_size: 4,
        quote: this.currentPrice,
        symbol,
      },
    };

    this.emit('tick', mockTick);
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * Check if authenticated
   */
  isAuthenticated(): boolean {
    return this.authenticated;
  }

  /**
   * Set current price (for testing)
   */
  setCurrentPrice(price: number): void {
    this.currentPrice = price;
  }

  /**
   * Trigger a specific tick (for testing)
   */
  triggerTick(symbol: string, price: number): void {
    const mockTick: TickData = {
      tick: {
        ask: price + 0.001,
        bid: price - 0.001,
        epoch: Math.floor(Date.now() / 1000),
        id: `mock_tick_${Date.now()}`,
        pip_size: 4,
        quote: price,
        symbol,
      },
    };

    this.emit('tick', mockTick);
  }

  /**
   * Simulate connection error
   */
  simulateConnectionError(): void {
    this.emit('error', new Error('Mock connection error'));
  }

  /**
   * Simulate disconnection
   */
  simulateDisconnection(): void {
    this.connected = false;
    this.authenticated = false;
    if (this.tickInterval) {
      clearInterval(this.tickInterval);
      this.tickInterval = null;
    }
    this.emit('disconnected');
  }
}
