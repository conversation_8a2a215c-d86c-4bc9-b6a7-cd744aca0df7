const WebSocket = require('ws');

console.log('🔍 Testing Deriv API Connection...');

const ws = new WebSocket('wss://ws.binaryws.com/websockets/v3?app_id=1089');

ws.on('open', function() {
    console.log('✅ Connected to Deriv API');
    
    // Test authorization
    const authMessage = {
        authorize: 'hEMSCZEtzrg5BzA',
        req_id: 1
    };
    
    console.log('🔐 Testing authorization...');
    ws.send(JSON.stringify(authMessage));
});

ws.on('message', function(data) {
    const message = JSON.parse(data);
    console.log('📨 Received:', JSON.stringify(message, null, 2));
    
    if (message.msg_type === 'authorize') {
        if (message.error) {
            console.log('❌ Authorization failed:', message.error);
        } else {
            console.log('✅ Authorization successful!');
            console.log('💰 Balance:', message.authorize.balance);
            console.log('💱 Currency:', message.authorize.currency);
            
            // Test getting balance
            const balanceMessage = {
                balance: 1,
                req_id: 2
            };
            console.log('💰 Testing balance request...');
            ws.send(JSON.stringify(balanceMessage));
        }
    } else if (message.msg_type === 'balance') {
        console.log('✅ Balance request successful!');
        console.log('💰 Current balance:', message.balance.balance);
        
        // Test tick subscription
        const tickMessage = {
            ticks: 'R_100',
            subscribe: 1,
            req_id: 3
        };
        console.log('📊 Testing tick subscription...');
        ws.send(JSON.stringify(tickMessage));
    } else if (message.msg_type === 'tick') {
        console.log('✅ Tick data received!');
        console.log('📈 Price:', message.tick.quote);
        console.log('🕐 Time:', new Date(message.tick.epoch * 1000).toLocaleTimeString());
        
        // Success! Close connection
        setTimeout(() => {
            console.log('🎉 All tests passed! API is working correctly.');
            ws.close();
        }, 2000);
    }
});

ws.on('error', function(error) {
    console.log('❌ WebSocket error:', error);
});

ws.on('close', function() {
    console.log('🔌 Connection closed');
});

// Timeout after 30 seconds
setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
}, 30000);
