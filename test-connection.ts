#!/usr/bin/env ts-node

import { DerivAPIService } from './src/services/DerivAPIService';
import { ConfigManager } from './src/config/ConfigManager';
import { Logger } from './src/utils/Logger';

const logger = Logger.getInstance();

async function testConnection() {
  logger.info('🔍 Testing Deriv API Connection...');
  
  try {
    // Get configuration
    const config = ConfigManager.getInstance();
    const derivConfig = config.getDerivAPIConfig();
    
    logger.info('📊 Using configuration:');
    logger.info(`- App ID: ${derivConfig.appId}`);
    logger.info(`- API Token: ${derivConfig.apiToken?.substring(0, 8)}...`);
    logger.info(`- API URL: ${derivConfig.apiUrl}`);
    
    // Create API service
    const api = new DerivAPIService(derivConfig);
    
    // Set up event listeners
    api.on('connected', () => {
      logger.info('✅ Connected to Deriv API');
    });
    
    api.on('authorized', () => {
      logger.info('✅ Authorized with Deriv API');
    });
    
    api.on('disconnected', () => {
      logger.info('❌ Disconnected from Deriv API');
    });
    
    api.on('error', (error) => {
      logger.error('❌ API Error:', error);
    });
    
    // Connect to API
    logger.info('🔌 Connecting to Deriv API...');
    await api.connect();

    // Authorize with API token
    logger.info('🔐 Authorizing with API token...');
    try {
      const authResult = await api.authorize();
      logger.info(`✅ Authorization successful: ${JSON.stringify(authResult)}`);
    } catch (error) {
      logger.error('❌ Authorization failed:', error instanceof Error ? error : new Error(String(error)));
      return;
    }

    // Test basic API calls
    logger.info('📊 Testing basic API calls...');

    // Test getting active symbols
    try {
      logger.info('📋 Getting active symbols...');
      const symbols = await api.getActiveSymbols();
      logger.info(`✅ Active symbols received: ${symbols.length} symbols`);
    } catch (error) {
      logger.error('❌ Get active symbols failed:', error instanceof Error ? error : new Error(String(error)));
    }

    // Test getting balance (this was failing before)
    try {
      logger.info('💰 Getting account balance...');
      const balance = await api.getBalance();
      logger.info(`✅ Account balance: ${JSON.stringify(balance)}`);
    } catch (error) {
      logger.error('❌ Get balance failed:', error instanceof Error ? error : new Error(String(error)));
    }
    
    // Test subscribing to ticks
    try {
      logger.info('📈 Testing tick subscription...');
      await api.subscribeTicks('R_100');
      logger.info('✅ Subscribed to R_100 ticks');
      
      // Listen for a few ticks
      let tickCount = 0;
      const maxTicks = 5;
      
      api.on('tick', (tick) => {
        tickCount++;
        logger.info(`📊 Received tick ${tickCount}/${maxTicks}:`, {
          symbol: tick.symbol,
          quote: tick.quote,
          epoch: tick.epoch
        });
        
        if (tickCount >= maxTicks) {
          logger.info('✅ Tick subscription test completed');
          cleanup();
        }
      });
      
      // Set a timeout in case we don't receive ticks
      setTimeout(() => {
        if (tickCount === 0) {
          logger.warn('⚠️ No ticks received within 30 seconds');
          cleanup();
        }
      }, 30000);
      
    } catch (error) {
      logger.error('❌ Tick subscription failed:', error instanceof Error ? error : new Error(String(error)));
      cleanup();
    }
    
    function cleanup() {
      logger.info('🧹 Cleaning up...');
      api.disconnect();
      setTimeout(() => {
        logger.info('✅ Connection test completed');
        process.exit(0);
      }, 1000);
    }
    
  } catch (error) {
    logger.error('💥 Connection test failed:', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('🔄 Received SIGINT, shutting down...');
  process.exit(0);
});

// Run the test
testConnection().catch((error) => {
  logger.error('💥 Test crashed:', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
});
