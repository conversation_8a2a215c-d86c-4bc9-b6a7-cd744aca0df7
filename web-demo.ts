import { WebBotManager } from './src/WebBotManager';
import { Logger } from './src/utils/Logger';

const logger = Logger.getInstance();

async function main() {
  logger.info('🌐 Starting Deriv Bot Web Interface...');
  
  const webBotManager = new WebBotManager();
  
  try {
    await webBotManager.start();
    
    logger.info('🎯 Web interface started successfully!');
    logger.info('📊 Dashboard available at: http://localhost:5173');
    logger.info('🔌 WebSocket server running on: http://localhost:3001');
    logger.info('💡 Use the web interface to start/stop the bot');
    logger.info('🛑 Press Ctrl+C to stop the web server');
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('🛑 Shutting down web interface...');
      await webBotManager.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      logger.info('🛑 Shutting down web interface...');
      await webBotManager.stop();
      process.exit(0);
    });
    
  } catch (error) {
    logger.error('💥 Failed to start web interface:', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

main().catch((error) => {
  logger.error('💥 Unhandled error:', error instanceof Error ? error : new Error(String(error)));
  process.exit(1);
});
